import { Outlet } from "react-router-dom";
import Navbar from "../../components/Navbar";
import SalesOfficerSidebar from "../../components/SalesOfficerSidebar";
import { useState, useEffect } from "react";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import LogoutModal from "../../components/LogoutModal";
import { checkTokenExpiration } from "../../redux/thunk";

export const SalesOfficerBasePage = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [tabNavigation, setTabNavigation] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [modals, setModals] = useState({
    logout: false,
  });

  useEffect(() => {
    if (token) {
      checkTokenExpiration();
    }
  }, [token]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      checkTokenExpiration();
    }, 900000); // 15 minutes

    return () => clearInterval(intervalId);
  }, []);

  const toggleTabNavigation = () => {
    setTabNavigation(!tabNavigation);
  };

  const openModal = (modalName: string) => {
    setModals((prevState) => ({ ...prevState, [modalName]: true }));
  };

  const closeModal = (modalName: string) => {
    setModals((prevState) => ({ ...prevState, [modalName]: false }));
  };

  return (
    <div className="lg:flex">
      <SalesOfficerSidebar
        showSidebar={showSidebar}
        setShowSidebar={setShowSidebar}
        tabNavigation={tabNavigation}
        toggleTabNavigation={toggleTabNavigation}
        openModal={() => openModal("logout")}
        role={[]}
        setSrc={() => {}}
      />
      <div
        className={`w-full ml-0 overflow-x-hidden transition-all duration-300 ${
          showSidebar ? "lg:ml-[280px]" : "lg:ml-[80px]"
        }`}
      >
        <Navbar
          tabNavigation={tabNavigation}
          toggleTabNavigation={toggleTabNavigation}
          showSidebar={showSidebar}
          setShowSidebar={setShowSidebar}
        />
        <div className="pt-[5rem] lg:pt-28 py-1 px-4 2xl:px-8">
          <Outlet />
        </div>
      </div>
      {modals.logout && <LogoutModal closeModal={() => closeModal("logout")} />}
    </div>
  );
};

export default SalesOfficerBasePage;
