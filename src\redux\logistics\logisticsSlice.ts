import { createSlice } from "@reduxjs/toolkit";
import {
  createDispatcher,
  getAllDispatch,
  logisticAssignDetails,
  logisticDashboard,
} from "../thunk";

const initialState: any = {
  allRiderInfo: [],
  dashboard: [],
  ordersToAssign: [],
  status: "idle",
  createDispatcherStatus: "idle",
  error: "",
};

const logisticsSlice = createSlice({
  name: "logistics",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAllDispatch.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(getAllDispatch.fulfilled, (state, action) => {
        state.allRiderInfo = action.payload;
        state.status = "success";
        state.error = "nil";
      })
      .addCase(getAllDispatch.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      })
      .addCase(logisticDashboard.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(logisticDashboard.fulfilled, (state, action) => {
        state.dashboard = action.payload;
        state.status = "success";
        state.error = "nil";
      })
      .addCase(logisticDashboard.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      })
      .addCase(logisticAssignDetails.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(logisticAssignDetails.fulfilled, (state, action) => {
        state.ordersToAssign = action.payload;
        state.status = "success";
        state.error = "nil";
      })
      .addCase(logisticAssignDetails.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      })
      .addCase(createDispatcher.pending, (state) => {
        state.createDispatcherStatus = "loading";
        state.error = "";
      })
      .addCase(createDispatcher.fulfilled, (state) => {
        state.createDispatcherStatus = "success";
        state.error = "";
      })
      .addCase(createDispatcher.rejected, (state, action) => {
        state.createDispatcherStatus = "failed";
        state.error = action.error?.message || "Unknown error";
      });
  },
});

export default logisticsSlice.reducer;
