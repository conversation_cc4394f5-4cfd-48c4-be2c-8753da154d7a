import { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import type { SideBarProps } from "../types/types";
import { RxDashboard } from "react-icons/rx";
import {
  MdKeyboardDoubleArrowLeft,
  MdKeyboardDoubleArrowRight,
} from "react-icons/md";
import { TbArrowsExchange, TbLogout2 } from "react-icons/tb";
import ChangePassword from "./modals/ChangePassword";
import { BiUser } from "react-icons/bi";
import { IoSettingsOutline } from "react-icons/io5";
import { FaShoppingCart } from "react-icons/fa";
import React from "react";

const SalesOfficerSidebar = ({
  tabNavigation,
  toggleTabNavigation,
  openModal,
  setShowSidebar,
  showSidebar,
}: SideBarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showChangePassword, setShowChangePassword] = useState(false);

  const handleOverlayClick = () => {
    if (tabNavigation) {
      toggleTabNavigation();
    }
  };

  const handleNavigation = (query: string) => {
    navigate(`/${query}`);
    if (tabNavigation) {
      toggleTabNavigation();
    }
  };

  const isActive = (path: string) => {
    return location.pathname === `/${path}`;
  };

  const sidebarItems = [
    {
      title: "General Overview",
      icon: <RxDashboard className="w-5 h-5" />,
      query: "sales-dashboard",
    },
    {
      title: "New Users",
      icon: <BiUser className="w-5 h-5" />,
      query: "sales-dashboard/new-users",
    },
    {
      title: "Users",
      icon: <BiUser className="w-5 h-5" />,
      query: "sales-dashboard/users",
    },
    {
      title: "Orders",
      icon: <FaShoppingCart className="w-5 h-5" />,
      query: "sales-dashboard/orders",
    },
  ];

  return (
    <>
      {/* Overlay for mobile */}
      <div
        className={`fixed top-0 h-screen transition ease-in-out inset-0 bg-black opacity-50 z-20 ${
          tabNavigation ? "block" : "hidden"
        }`}
        onClick={handleOverlayClick}
      ></div>

      {/* Sidebar */}
      <aside
        className={`overflow-y-auto shadow-sm fixed bg-white h-full z-30 transition-all duration-300 ${
          !tabNavigation ? "hidden lg:block" : "block animate-fadeIn"
        } ${showSidebar ? "w-[280px]" : "w-[80px]"}`}
      >
        <div className="w-full h-full flex flex-col">
          {/* Logo section */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center">
              <div className="bg-primary p-2 rounded-md">
                <img src="/assets/logo.png" alt="logo" className="w-8 h-8" />
              </div>
              {showSidebar && (
                <div className="ml-2">
                  <h1 className="text-secondary font-bold text-xl">
                    FOOD BANK
                  </h1>
                  <p className="text-xs text-secondary">MAKING LIFE EASY</p>
                </div>
              )}
            </div>
            <div className="flex items-center">
              <button
                onClick={() => setShowSidebar(!showSidebar)}
                className="text-gray-500 hidden lg:block mr-2"
                aria-label={showSidebar ? "Collapse sidebar" : "Expand sidebar"}
              >
                {showSidebar ? (
                  <MdKeyboardDoubleArrowLeft className="w-6 h-6" />
                ) : (
                  <MdKeyboardDoubleArrowRight className="w-6 h-6" />
                )}
              </button>
              <button
                onClick={toggleTabNavigation}
                className="text-gray-500 lg:hidden"
                aria-label="Close sidebar"
              >
                <MdKeyboardDoubleArrowLeft className="w-6 h-6" />
              </button>
            </div>
          </div>
          {/* Navigation items */}
          <nav className="flex-1 py-6">
            <ul className={`space-y-1 ${showSidebar ? "px-3" : "px-1"}`}>
              {sidebarItems.map((item, index) => (
                <li key={index}>
                  <button
                    onClick={() => handleNavigation(item.query)}
                    className={`flex items-center w-full ${
                      showSidebar ? "px-4" : "px-2 justify-center"
                    } py-3 text-sm rounded-md transition-colors ${
                      isActive(item.query)
                        ? "bg-[#F3F9FB] text-secondary font-medium"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <span
                      className={`${
                        isActive(item.query) ? "text-primary" : "text-gray-500"
                      }`}
                    >
                      {item.icon}
                    </span>
                    {showSidebar && <span className="ml-3">{item.title}</span>}
                  </button>
                </li>
              ))}
            </ul>
          </nav>
          {/* Bottom section */}
          <div
            className={`mt-auto border-t pt-4 ${
              showSidebar ? "px-4" : "px-2"
            } pb-6`}
          >
            <div className="space-y-3">
              <button
                onClick={() => setShowChangePassword(true)}
                className={`flex items-center w-full ${
                  showSidebar ? "px-4" : "px-2 justify-center"
                } py-2 text-sm text-gray-700 rounded-md hover:bg-gray-100`}
              >
                <TbArrowsExchange className="w-5 h-5 text-gray-500" />
                {showSidebar && <span className="ml-3">Change Password</span>}
              </button>

              <button
                onClick={() => openModal()}
                className={`flex items-center w-full ${
                  showSidebar ? "px-4" : "px-2 justify-center"
                } py-2 text-sm text-gray-700 rounded-md hover:bg-gray-100`}
              >
                <TbLogout2 className="w-5 h-5 text-gray-500" />
                {showSidebar && <span className="ml-3">Log Out</span>}
              </button>

              <button
                onClick={() => handleNavigation("sales-dashboard/settings")}
                className={`flex items-center w-full ${
                  showSidebar ? "px-4" : "px-2 justify-center"
                } py-2 text-sm text-gray-700 rounded-md hover:bg-gray-100`}
              >
                <IoSettingsOutline className="w-5 h-5 text-gray-500" />
                {showSidebar && <span className="ml-3">Settings</span>}
              </button>
            </div>
          </div>
        </div>
      </aside>

      {/* Change Password Modal */}
      {showChangePassword && (
        <ChangePassword setShowChangePassword={setShowChangePassword} />
      )}
    </>
  );
};

export default SalesOfficerSidebar;
