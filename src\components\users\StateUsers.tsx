import { useEffect, useState } from "react";
import Pagination from "../Pagination";
import { IoAdd, IoSearchOutline } from "react-icons/io5";
import { FaRegCopy } from "react-icons/fa6";
import { fetchCategories, formatNumber } from "../../redux/thunk";
import StateGovModal from "../modals/StateGovModal";

const StateUsers = ({
  stateUsers,
  status,
  isLoadingDelete = false,
  handleDeleteClick = () => {},
  handleCopyClick = () => {},
  handleBvnClick,
  handleViewAllBank,
  handleAddBank,
  handleUpdateCreditLimit,
}: any) => {
  const [searchedUser, setSearchedUser] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [itemsPerPage] = useState(20);
  const [filter, setFilter] = useState("");
  const [allMinistries, setAllMinistries] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showEmploymentDetails, setShowEmploymentDetails] = useState(false);
  const [employmentDetails, setEmploymentDetails] = useState({});

  const handleEmploymentDetails = (data: any) => {
    setEmploymentDetails(data);
    setShowEmploymentDetails(true);
  };

  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems = stateUsers.slice(indexOfFirstPost, indexOfLastPost);

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(stateUsers.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const searchForUsersWithEmail = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const searchResult =
        stateUsers?.filter((user: any) =>
          user.email?.toLowerCase().includes(value.toLowerCase())
        ) || null;
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  useEffect(() => {
    const fetchAndSetCategories = async () => {
      const allCategories = await fetchCategories();
      setAllMinistries(allCategories);
    };
    fetchAndSetCategories();
  }, []);

  useEffect(() => {
    if (filter) {
      const filteredByName = stateUsers.filter(
        (user: any) => user.stateGovernmentData.ministryId === filter
      );
      setFilteredUsers(filteredByName);
    } else {
      setFilteredUsers(stateUsers);
    }
  }, [filter, stateUsers]);

  const mainData = searchedUser.length > 0 ? searchResults : currentItems;
  const adminLevel = sessionStorage.getItem("adminLevel");

  return (
    <div className="bg-white rounded-md shadow-md pb-6">
      <h1 className="text-base font-semibold ">State Users</h1>

      <div
        className={`w-full overflow-x-auto ${
          status === "loading" && "animate-pulse h-[50vh]"
        }`}
      >
        <div
          className="flex items-center justify-between p-6"
          style={{ minWidth: "700px" }}
        >
          <div className="flex justify-between w-full">
            <div className="relative md:w-[30rem] w-fit">
              <IoSearchOutline className="w-6 h-6 absolute top-[0.6rem] left-2 text-gray-300" />
              <input
                type="search"
                name="searchedUser"
                id="searchedUser"
                value={searchedUser}
                onChange={(e) => searchForUsersWithEmail(e.target.value)}
                placeholder="Search user using email"
                className="border p-2 text-sm rounded-md indent-7 w-full"
                disabled={stateUsers.length === 0}
              />
            </div>
            <div>
              <select
                className="border p-2 rounded-md text-sm"
                onChange={(e) => setFilter(e.target.value)}
                value={filter}
                name="filter"
              >
                <option value="">All ministries</option>
                {allMinistries.map((ministry: any, index: number) => (
                  <option key={index} value={ministry._id}>
                    {ministry.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <section className="overflow-x-auto">
          <table className="w-[1020px]" style={{ minWidth: "700px" }}>
            <thead className="bg-gray-50 font-bold p-4 text-left">
              <tr>
                <th className="p-2 ">S/N</th>
                <th className="p-2 text-nowrap">Name</th>
                <th className="p-2 text-nowrap">Email</th>
                <th className="p-2 text-nowrap">Phone Number</th>
                <th className="p-2 text-nowrap">Ministry</th>
                <th className="p-2">BVN</th>
                <th className="p-2 text-nowrap">Add Bank</th>
                <th className="p-2 text-nowrap">All Banks</th>
                <th className="p-2 text-nowrap">Employment</th>
                <th className="p-2 text-nowrap">Credit Score(Balance)</th>
                <th className="p-2 text-nowrap">Credit Score</th>
                <th className="p-2 text-nowrap">Open Password</th>
                <th className="p-2 text-nowrap">Action</th>
                {adminLevel === "superadmin" && (
                  <th className="p-2 text-nowrap">Force Delete User</th>
                )}
              </tr>
            </thead>
            <tbody className="px-4">
              {filter.length > 0 && filteredUsers.length === 0 ? (
                <tr className="p-2">
                  <td colSpan={10} className="text-sm text-center">
                    No user in this ministry
                  </td>
                </tr>
              ) : mainData.length > 0 ? (
                mainData.map((data: any, index: any) => (
                  <tr className="border-b border-gray-300 py-2" key={index}>
                    <td className="text-secondary p-2">
                      {index + indexOfFirstPost + 1}
                    </td>
                    <td className="p-2 text-nowrap">
                      {data.lastName} {data.firstName}
                    </td>
                    <td className="p-2">
                      <div className="flex justify-between gap-2 items-center">
                        <p>{data.email}</p>
                        <button
                          type="button"
                          className="mr-3"
                          onClick={() => handleCopyClick(data.email)}
                        >
                          <FaRegCopy className="w-5 h-5 text-gray-500" />
                        </button>
                      </div>
                    </td>
                    <td className="p-2 text-nowrap">{data.phoneNumber}</td>
                    <td className="p-2">
                      {
                        data.stateGovernmentData.stateGovernmentId.categoryType.filter(
                          (ministry: any) =>
                            data.stateGovernmentData.ministryId === ministry._id
                        )[0].name
                      }
                    </td>
                    <td className="p-2">
                      <div className="flex items-center gap-2 justify-between">
                        <button
                          onClick={() => handleBvnClick(data)}
                          className="text-blue-500 hover:underline focus:outline-none"
                        >
                          {data.bvn}
                        </button>
                        <button
                          type="button"
                          className=""
                          onClick={() => handleCopyClick(data.bvn)}
                        >
                          <FaRegCopy className="w-5 h-5 text-gray-500" />
                        </button>
                      </div>
                    </td>
                    <td className="p-2 text-center">
                      <button
                        className="text-secondary border"
                        onClick={() =>
                          handleAddBank(data.firstName, data.lastName, data._id)
                        }
                      >
                        <IoAdd className="w-9 h-9" />
                      </button>
                    </td>
                    <td className="p-2 text-center">
                      <button
                        className="bg-secondary text-white text-sm rounded-md p-2"
                        onClick={() =>
                          handleViewAllBank(data._id, data.linkedBanks)
                        }
                      >
                        View
                      </button>
                    </td>
                    <td className="p-2 text-nowrap">
                      <button
                        onClick={() =>
                          handleEmploymentDetails(data.stateGovernmentData)
                        }
                        className="text-secondary hover:underline focus:outline-none text-center"
                      >
                        View
                      </button>
                    </td>
                    <td className="p-2">₦{formatNumber(data.creditScore)}</td>
                    <td className="p-2">₦{formatNumber(data.creditLimit)}</td>
                    <td className="p-2">
                      {data.userPassword ? data.userPassword : "not found"}
                    </td>
                    <td className="p-2 text-nowrap">
                      <button
                        type="button"
                        className="p-2 text-sm text-secondary border border-secondary"
                        onClick={() => handleUpdateCreditLimit(data)}
                      >
                        Update Credit Limit
                      </button>
                    </td>
                    {adminLevel === "superadmin" && (
                      <td className="p-2 text-center">
                        <button
                          type="button"
                          className={`${
                            isLoadingDelete ? "bg-red-100" : "bg-red-600"
                          } p-2 text-sm text-white rounded-md`}
                          onClick={() => handleDeleteClick(data)}
                          disabled={isLoadingDelete}
                        >
                          Delete
                        </button>
                      </td>
                    )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={10} className="text-center text-secondary p-3">
                    User not found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </section>
      </div>
      {showEmploymentDetails && (
        <StateGovModal
          selectedInfo={employmentDetails}
          setStateGovModalOpen={setShowEmploymentDetails}
        />
      )}
      <section className="p-3 my-5">
        <Pagination
          length={
            searchResults.length > 0
              ? searchResults.length
              : filter.length > 0
              ? filteredUsers.length
              : stateUsers.length
          }
          itemsPerPage={itemsPerPage}
          handlePagination={handlePagination}
          currentPage={currentPage}
          prevPage={prevPage}
          nextPage={nextPage}
        />
      </section>
    </div>
  );
};

export default StateUsers;
