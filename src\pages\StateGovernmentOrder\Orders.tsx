import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
// Assuming getSingleCustomer might be needed if detailed user info beyond name/email is required later,
// otherwise, it can be removed if userId object in the order already contains all needed info.
import {
  approveOrder,
  getOrders,
  getAllVendors,
  getSingleCustomer,
} from "../../redux/thunk";
import toast from "react-hot-toast";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import OrderDetails from "../../components/modals/OrderDetails"; // Re-uses the enhanced modal
import axios from "axios";

import { RootState } from "../../redux/store";
import Pagination from "../../components/Pagination";
import { FaRegCopy } from "react-icons/fa6";
import { Preloader } from "../../components/elements/Preloader";
import { IoSearchOutline } from "react-icons/io5";

// --- Helper Function for Date Calculation (Copied from previous enhancement) ---
const getFormattedDateOffset = (daysOffset: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + daysOffset);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};
// --- ---

export const StateGovernmentOrders = () => {
  const [viewDetails, setViewDetails] = useState(false);
  const [viewCancelOrder, setViewCancelOrder] = useState({
    details: {},
    status: false,
    btn: false,
  });
  const [cancelReason, setCancelReason] = useState("");
  const [orderDetails, setOrderDetails] = useState<any>({});
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [pickupDetails, setPickupDetails] = useState<any>([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [deliveryDateFilter, setDeliveryDateFilter] = useState<string>("");
  const [error, setError] = useState<any>({});
  const [stateGovOrdersList, setStateGovOrdersList] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [searchedUser, setSearchedUser] = useState("");
  const { token } = useAppSelector((store: RootState) => store.auth);

  const dispatch = useAppDispatch();
  const { orders } = useAppSelector((store) => store.orders);
  const ordersLoading = false;

  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;

  useEffect(() => {
    dispatch(getOrders());
    dispatch(getAllVendors());
  }, [dispatch]);

  // Filter for State Government Orders
  useEffect(() => {
    if (orders.length > 0) {
      const stateGovOrders = orders.filter(
        (order: any) =>
          order.userId && order.userId.categoryType === "stateGovernment"
      );
      setStateGovOrdersList(stateGovOrders);
    } else {
      setStateGovOrdersList([]);
    }
  }, [orders]);

  useEffect(() => {
    const stateGovUserIds = stateGovOrdersList
      .map((item: any) => item?.userId?.id)
      .filter(Boolean);

    const fetchUsersData = async () => {
      const currentlyLoadedUserIds = users.map((u) => u?.customer?.id);
      const idsToFetch = stateGovUserIds.filter(
        (id) => !currentlyLoadedUserIds.includes(id)
      );

      if (idsToFetch.length > 0) {
        const usersDataPromises = idsToFetch.map(async (id: any) => {
          try {
            const response = await dispatch(getSingleCustomer(id));
            return response.payload?.customer ? response.payload : null;
          } catch (fetchError) {
            console.error(`Failed to fetch user ${id}:`, fetchError);
            return null;
          }
        });
        const results = await Promise.all(usersDataPromises);
        const newUsers = results.filter(Boolean);
        setUsers((prevUsers) => {
          const existingIds = new Set(prevUsers.map((u) => u?.customer?.id));
          const uniqueNewUsers = newUsers.filter(
            (nu) => !existingIds.has(nu?.customer?.id)
          );
          return [...prevUsers, ...uniqueNewUsers];
        });
        // setIsLoading(false);
      }
    };

    if (stateGovUserIds.length > 0) {
      fetchUsersData();
    }
  }, [dispatch, stateGovOrdersList]);

  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, deliveryDateFilter, searchedUser]);

  const validateOrderApproval = () => {
    let isValid = true;
    let newError: any = {};
    setError({});

    pickupDetails.forEach((item: any) => {
      if (!item.vendorId || item.vendorId.length === 0) {
        newError.pickupDetails = "Please select a vendor for all items.";
        isValid = false;
      }
    });
    if (!isValid) {
      setError(newError);
    }
    return isValid;
  };

  const handleCopyClick = (text: string | null | undefined) => {
    if (!text) return;
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const handleInitialApprove = async (
    id: string,
    pickUpDetailsInput: string
  ) => {
    setError({});
    if (pickUpDetailsInput.trim().length === 0) {
      setError({ initialPickup: "Enter pickup location" });
      return;
    }

    const payload = {
      orderId: id,
      pickUpDetails: pickUpDetailsInput,
    };

    setIsActionLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/approveOrderInitial`,
        payload,
        { headers: { Authorization: token } }
      );
      toast.success(res.data.message || "Order approved successfully");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Initial Approve Error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to approve order!"
      );
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleApproveOrder = async (id: string) => {
    if (!validateOrderApproval()) {
      return;
    }

    setIsActionLoading(true);
    try {
      await dispatch(
        approveOrder({ orderId: id, pickUpDetails: pickupDetails })
      ).unwrap();

      toast.success("Order approved successfully!");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Approve Order Error:", error);
      toast.error(error?.message || "Failed to approve order!");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    if (cancelReason.trim().length === 0) {
      toast.error("Please provide a reason for cancellation.");
      return;
    }

    setViewCancelOrder((prev) => ({ ...prev, btn: true }));
    setIsActionLoading(true);

    const payload = { cancelReason };

    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/cancelOrder/order/${orderId}`,
        payload,
        { headers: { Authorization: token } }
      );
      toast.success(res.data.message || "Order canceled successfully");
      dispatch(getOrders()); // Refresh orders
      setViewCancelOrder({ details: {}, status: false, btn: false });
      setCancelReason("");
    } catch (error: any) {
      console.error("Cancel Order error", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to cancel order"
      );
      setViewCancelOrder((prev) => ({ ...prev, btn: false }));
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleViewDetails = (orderData: any) => {
    setOrderDetails(orderData);
    setViewDetails(true);
    setError({});
  };

  const getFilteredAndSearchedOrders = () => {
    let processedOrders = [...stateGovOrdersList];

    // 1. Filter by Delivery Date (deliveryOption)
    if (deliveryDateFilter) {
      processedOrders = processedOrders.filter((order: any) => {
        let targetDateStr = "";
        if (deliveryDateFilter === "today") {
          targetDateStr = getFormattedDateOffset(0);
        } else {
          const days = parseInt(deliveryDateFilter.replace("day", ""), 10);
          if (!isNaN(days)) {
            targetDateStr = getFormattedDateOffset(days);
          }
        }
        // Compare, assuming deliveryOption is "YYYY-MM-DD"
        return order.deliveryOption && order.deliveryOption === targetDateStr;
      });
    }

    // 2. Filter by Status
    if (statusFilter) {
      processedOrders = processedOrders.filter(
        (order: any) => order.status === statusFilter
      );
    }

    // 3. Apply Search Filter (if any)
    if (searchedUser.length > 0) {
      processedOrders = processedOrders.filter((order: any) =>
        order.userId?.email?.toLowerCase().includes(searchedUser.toLowerCase())
      );
    }

    return processedOrders.slice().reverse();
  };

  const finalFilteredOrders = getFilteredAndSearchedOrders();

  // --- Pagination Logic using finalFilteredOrders ---
  const currentItems = finalFilteredOrders.slice(
    indexOfFirstPost,
    indexOfLastPost
  );

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev > 1 ? prev - 1 : prev));
  };

  const nextPage = () => {
    setCurrentPage((prev) =>
      prev < Math.ceil(finalFilteredOrders.length / itemsPerPage)
        ? prev + 1
        : prev
    );
  };

  return (
    <main className="p-4 md:p-6">
      <h2 className="font-semibold text-xl mb-4">State Government Orders</h2>

      {/* Search Input */}
      <div className="relative w-full max-w-lg text-sm mb-5">
        <IoSearchOutline className="w-5 h-5 absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400" />
        <input
          type="search"
          name="searchedUser"
          id="searchedUser"
          value={searchedUser}
          onChange={(e) => setSearchedUser(e.target.value)}
          placeholder="Search by client email..."
          className="border border-gray-300 p-2 rounded-md pl-10 w-full focus:outline-none focus:ring-1 focus:ring-secondary"
          disabled={stateGovOrdersList.length === 0 && !ordersLoading}
        />
      </div>

      {/* Filters Row */}
      <div className="my-6 flex flex-wrap items-center justify-between gap-4 bg-gray-50 p-4 rounded-md">
        <h3 className="text-md font-medium text-gray-700">
          Displaying: {finalFilteredOrders.length} order(s)
        </h3>
        <div className="flex flex-wrap items-center gap-4">
          {/* --- Delivery Date Filter --- */}
          <label
            htmlFor="deliveryDateFilter"
            className="flex items-center text-sm"
          >
            <span className="mr-2 text-gray-600">Delivery:</span>
            <select
              name="deliveryDateFilter"
              id="deliveryDateFilter"
              className="border border-gray-300 p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary bg-white"
              value={deliveryDateFilter}
              onChange={(e) => setDeliveryDateFilter(e.target.value)}
            >
              <option value="">All Dates</option>
              <option value="today">Today</option>
              <option value="1day">1 Day</option>
              <option value="2day">2 Days</option>
              <option value="3day">3 Days</option>
              <option value="4day">4 Days</option>
              <option value="5day">5 Days</option>
              <option value="6day">6 Days</option>
              <option value="7day">7 Days</option>
            </select>
          </label>

          {/* Status Filter */}
          <label htmlFor="statusFilter" className="flex items-center text-sm">
            <span className="mr-2 text-gray-600">Status:</span>
            <select
              name="statusFilter"
              id="statusFilter"
              className="border border-gray-300 p-2 ml-1 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary bg-white"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="delivered">Delivered</option>
              <option value="cancel">Canceled</option>
              <option value="rider">Rider</option>
            </select>
          </label>
        </div>
      </div>

      {/* Orders Table */}
      <section className="w-full bg-white p-3 rounded-lg shadow-md overflow-x-auto">
        <table className="w-full min-w-[900px]">
          {" "}
          {/* Adjusted min-width */}
          <thead className="text-left bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="p-3 text-sm font-semibold text-gray-600">SN</th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Client
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">Email</th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Order No.
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Total Price
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Status
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">View</th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {ordersLoading && stateGovOrdersList.length === 0 ? (
              <tr>
                <td colSpan={8} className="text-center p-10">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : currentItems.length > 0 ? (
              currentItems.map((order: any, index: number) => (
                <tr
                  className="hover:bg-gray-50 text-sm"
                  key={order._id || index}
                >
                  <td className="p-3 font-medium text-secondary">
                    {index + indexOfFirstPost + 1}
                  </td>
                  <td className="p-3 capitalize">
                    {`${order.userId?.firstName?.toLowerCase() || ""} ${
                      order.userId?.lastName?.toLowerCase() || ""
                    }`.trim() || "N/A"}
                  </td>
                  <td className="p-3">
                    <div className="flex gap-2 items-center">
                      {order.userId?.email && (
                        <button
                          type="button"
                          className="text-gray-400 hover:text-secondary"
                          title={`Copy ${order.userId.email}`}
                          onClick={() => handleCopyClick(order.userId.email)}
                        >
                          <FaRegCopy className="w-4 h-4" />
                        </button>
                      )}
                      <span>{order.userId?.email || "N/A"}</span>
                    </div>
                  </td>
                  <td className="p-3 text-gray-600">
                    {order.orderNumber || "N/A"}
                  </td>
                  <td className="p-3 font-medium">
                    ₦{order.allItemsTotalPrice?.toLocaleString() || 0}
                  </td>
                  <td className="p-3">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium capitalize ${
                        order.status === "approved"
                          ? "bg-green-100 text-green-700"
                          : order.status === "pending"
                          ? "bg-yellow-100 text-yellow-700"
                          : order.status === "delivered"
                          ? "bg-blue-100 text-blue-700"
                          : order.status === "cancel"
                          ? "bg-red-100 text-red-700"
                          : order.status === "rider"
                          ? "bg-purple-100 text-purple-700"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {order.status === "cancel" ? "Canceled" : order.status}
                    </span>
                  </td>
                  <td className="p-3">
                    <button
                      type="button"
                      className="px-3 py-1 bg-secondary text-white rounded text-xs hover:bg-secondary-dark transition-colors"
                      onClick={() => handleViewDetails(order)}
                    >
                      View Details
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      type="button"
                      className={`px-3 py-1 rounded text-xs ${
                        order.status !== "pending"
                          ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                          : "text-red-600 border border-red-500 hover:bg-red-50 transition-colors"
                      }`}
                      onClick={() =>
                        setViewCancelOrder({
                          details: order,
                          status: true,
                          btn: false,
                        })
                      }
                      disabled={order.status !== "pending"}
                    >
                      {order.status === "cancel" ? "Canceled" : "Cancel Order"}
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center text-gray-500 p-6">
                  {searchedUser
                    ? "Client email not found."
                    : "No orders match the current filters."}
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {/* Pagination */}
        {finalFilteredOrders.length > itemsPerPage && (
          <section className="pt-4 mt-4 border-t border-gray-200">
            <Pagination
              length={finalFilteredOrders.length}
              itemsPerPage={itemsPerPage}
              handlePagination={handlePagination}
              currentPage={currentPage}
              prevPage={prevPage}
              nextPage={nextPage}
            />
          </section>
        )}
      </section>

      {/* Cancel Order Modal */}
      {viewCancelOrder.status && (
        <div className="fixed z-50 inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-auto">
            <h3 className="text-lg font-semibold mb-2 text-gray-800">
              Cancel Order Confirmation
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Please provide a reason for canceling this order:
            </p>
            <textarea
              name="reason"
              id="reason"
              className="w-full border border-gray-300 p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary"
              placeholder="Enter reason..."
              rows={3}
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
            ></textarea>
            <div className="flex justify-end gap-3 text-sm mt-5">
              <button
                type="button"
                className="py-2 px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                onClick={() => {
                  setCancelReason("");
                  setViewCancelOrder({
                    details: {},
                    btn: false,
                    status: false,
                  });
                }}
              >
                Back
              </button>
              <button
                type="button"
                className={`py-2 px-4 w-28 flex justify-center items-center rounded-md text-white transition-colors ${
                  cancelReason.trim().length === 0 ||
                  viewCancelOrder.btn ||
                  isActionLoading
                    ? "bg-red-300 cursor-not-allowed"
                    : "bg-red-600 hover:bg-red-700"
                }`}
                onClick={() =>
                  handleCancelOrder((viewCancelOrder.details as any)?._id)
                }
                disabled={
                  cancelReason.trim().length === 0 ||
                  viewCancelOrder.btn ||
                  isActionLoading
                }
              >
                {viewCancelOrder.btn || isActionLoading ? (
                  <Preloader />
                ) : (
                  "Proceed"
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {viewDetails && (
        <OrderDetails
          accountType="stateGovernment"
          orderDetails={orderDetails}
          setViewDetails={setViewDetails}
          pickupDetails={pickupDetails}
          setPickupDetails={setPickupDetails}
          handleApproveOrder={handleApproveOrder}
          handleInitialApprove={handleInitialApprove}
          error={error}
          setError={setError}
          isLoading={isActionLoading}
        />
      )}
    </main>
  );
};
