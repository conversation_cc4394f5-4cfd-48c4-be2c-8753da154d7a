import { formatNumber } from "../../redux/thunk";

const StateGovModal = ({ setStateGovModalOpen, selectedInfo }: any) => {
  return (
    <div className="fixed z-40 inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50">
      <div className="bg-white p-4 rounded-md" style={{ width: "500px" }}>
        <div className="flex justify-between items-center  text-dark p-2 rounded-t-md mb-4">
          <h2 className="text-lg font-semibold">User Information </h2>
          <button
            onClick={() => setStateGovModalOpen(false)}
            className="text-dark"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className=" border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">Ministry:</h4>{" "}
          <h5>
            {selectedInfo.ministryId &&
            selectedInfo.stateGovernmentId &&
            Array.isArray(selectedInfo.stateGovernmentId.categoryType)
              ? selectedInfo.stateGovernmentId.categoryType.find(
                  (item: any) => item._id === selectedInfo.ministryId
                )?.name || "not found"
              : "not found"}
          </h5>
        </div>
        <div className=" border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">Staff ID:</h4>{" "}
          <h5>{selectedInfo.staffId ? selectedInfo.staffId : "not found"}</h5>
        </div>
        <div className="border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">Monthly Salary:</h4>{" "}
          <h5>
            {selectedInfo.monthlySalary
              ? formatNumber(selectedInfo.monthlySalary)
              : "not found"}
          </h5>
        </div>
        <div className="border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">
            Salary Account Bank Name:
          </h4>{" "}
          <h5>
            {selectedInfo.salaryAccountBankName
              ? selectedInfo.salaryAccountBankName
              : "not found"}{" "}
          </h5>
        </div>
        <div className="border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">Salary Account Name</h4>{" "}
          <h5>
            {selectedInfo.salaryAccountName
              ? selectedInfo.salaryAccountName
              : "not found"}
          </h5>
        </div>
        <div className="border-b border-gray-300 mb-3 text-sm flex justify-between">
          <h4 className="font-semibold capitalize">Salary Account Number</h4>{" "}
          <h5>
            {selectedInfo.salaryAccountNumber
              ? selectedInfo.salaryAccountNumber
              : "not found"}
          </h5>
        </div>
      </div>
    </div>
  );
};

export default StateGovModal;
