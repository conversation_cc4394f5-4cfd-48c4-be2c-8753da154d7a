import { BiSearchAlt } from "react-icons/bi";

const InactiveComponent = ({
  setSelectedData,
  setSelectedSubdata,
  selectedData,
  data,
  setShowCreateModal,
  setCreateMinistry,
  setCategoryId,
  setSubCategoryId,
  path,
}: {
  setSelectedData: any;
  setSelectedSubdata: any;
  setSubCategoryId: any;
  setCategoryId: any;
  createMinistry: boolean;
  selectedData: {
    category: string;
    categoryIcon: string;
    categoryType?: [
      {
        name: string;
        _id: string;
      }
    ];
    _id?: string;
  };
  data: any[];
  setShowCreateModal: (prev: boolean) => void;
  setCreateMinistry: (prev: boolean) => void;
  path: string;
}) => {
  const adminLevel = sessionStorage.getItem("adminLevel");
  const subData = selectedData.categoryType;

  return (
    <section>
      {adminLevel === "superadmin" && (
        <section className="flex justify-end p-2 gap-3">
          <button
            type="button"
            className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
            onClick={() => {
              setShowCreateModal(true);
              setCreateMinistry(true);
            }}
          >
            Create {path === "stateGovernment" && "Ministry"}
          </button>

          <button
            type="button"
            className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
            onClick={() => setShowCreateModal(true)}
          >
            Create {path === "stateGovernment" ? "State" : "Bank"}
          </button>
        </section>
      )}

      <section className="text-center text-sm shadow-md p-2 pb-4 font-mont font-medium w-fit">
        <h4>Select {path === "stateGovernment" ? "State" : ""}</h4>
        <div className="relative my-3">
          <BiSearchAlt className="absolute w-5 h-5 opacity-70 top-2 left-2" />
          <input
            type="search"
            name="search"
            id="search"
            placeholder="Search"
            className="border rounded-md p-2 indent-5 w-full"
          />
        </div>
        <section>
          {data ? (
            data.map((item, index) => (
              <section
                key={index}
                className="flex items-center justify-between border-b-2 p-2 cursor-pointer relative"
                onClick={() => {
                  setSelectedData(item);
                  setCategoryId(item._id);
                }}
              >
                <div className="flex gap-3 items-center">
                  <img
                    src={item.categoryIcon}
                    alt={item.category}
                    className="w-10 h-10 bg-cover rounded-[50%]"
                  />
                  <p>{item.category}</p>
                </div>
              </section>
            ))
          ) : (
            <p>Nothing to show</p>
          )}
          <section
            className={`${
              selectedData.category ? "block" : "hidden"
            } text-center text-sm shadow-md p-2 font-mont font-medium absolute top-1/3 left-[30rem] bg-white w-fit`}
          >
            <h4>Select {path === "stateGovernment" ? "Ministry" : "Bank"}</h4>
            {/* <h4>
              Select {selectedData.category}{" "}
              {path === "stateGovernment" ? "Ministry" : "Branch"}
            </h4> */}
            <div className="relative my-3">
              <BiSearchAlt className="absolute w-5 h-5 opacity-70 top-2 left-2" />
              <input
                type="search"
                name="search"
                id="search"
                placeholder="Search"
                className="border rounded-md p-2 indent-5 w-full"
              />
            </div>
            <section>
              {subData &&
                subData.map((item: any, index: any) => (
                  <section
                    key={index}
                    className="flex items-center justify-between border-b-2 p-2 cursor-pointer"
                    onClick={() => {
                      const radio = document.getElementById(
                        `radio-${index}`
                      ) as HTMLInputElement;
                      if (radio) {
                        radio.checked = true;
                      }
                      setSelectedSubdata(item.name);
                      setSubCategoryId(item._id);
                    }}
                  >
                    <div className="flex gap-3 items-center">
                      <p>{item.name}</p>
                    </div>
                    <input
                      type="radio"
                      name="state"
                      id={`radio-${index}`}
                      className="peer hidden"
                    />
                    <label
                      htmlFor={`radio-${index}`}
                      className="w-5 h-5 items-center justify-center hidden peer-checked:flex peer-checked:text-secondary"
                    >
                      ✔
                    </label>
                  </section>
                ))}
            </section>
          </section>
        </section>
      </section>
    </section>
  );
};

export default InactiveComponent;
