import React, { useState } from "react";
import toast from "react-hot-toast";
import { FaPlus, FaTimes, FaUpload } from "react-icons/fa";

interface CategoryType {
  name: string;
}

interface CreateCategoryModalProps {
  onClose: () => void;
  onCategoryCreated: () => void;
}

const CreateCategoryModal: React.FC<CreateCategoryModalProps> = ({
  onClose,
  onCategoryCreated,
}) => {
  const [formData, setFormData] = useState({
    category: "",
    categoryIcon: "",
  });
  const [categoryTypes, setCategoryTypes] = useState<CategoryType[]>([
    { name: "" },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCategoryTypeChange = (index: number, value: string) => {
    const updatedTypes = [...categoryTypes];
    updatedTypes[index].name = value;
    setCategoryTypes(updatedTypes);
  };

  const addCategoryType = () => {
    setCategoryTypes([...categoryTypes, { name: "" }]);
  };

  const removeCategoryType = (index: number) => {
    if (categoryTypes.length > 1) {
      const updatedTypes = categoryTypes.filter((_, i) => i !== index);
      setCategoryTypes(updatedTypes);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploadingImage(true);
    const formDataUpload = new FormData();
    formDataUpload.append("file", file);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/upload/file`,
        {
          method: "POST",
          body: formDataUpload,
        }
      );

      if (response.ok) {
        const data = await response.json();
        setFormData((prev) => ({
          ...prev,
          categoryIcon: data.url,
        }));
        toast("Image uploaded successfully");
      } else {
        toast("Failed to upload image");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Error uploading image");
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.category.trim()) {
      toast.error("Category name is required");
      return;
    }

    if (!formData.categoryIcon.trim()) {
      toast.error("Category icon is required");
      return;
    }

    const validCategoryTypes = categoryTypes.filter((type) => type.name.trim());

    if (validCategoryTypes.length === 0) {
      toast.error("At least one category type is required");
      return;
    }

    setIsLoading(true);

    try {
      const payload = {
        category: formData.category.trim(),
        categoryIcon: formData.categoryIcon,
        categoryType: validCategoryTypes,
      };

      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (response.ok) {
        toast.success("Category created successfully");
        onCategoryCreated();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to create category");
      }
    } catch (error) {
      console.error("Error creating category:", error);
      toast.error("Error creating category");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-bold text-gray-800">
            Create New Category
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Category Name */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name *
            </label>
            <input
              type="text"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              placeholder="Enter category name"
              required
            />
          </div>

          {/* Category Icon */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Icon *
            </label>
            <div className="flex items-center gap-4">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                id="imageUpload"
              />
              <label
                htmlFor="imageUpload"
                className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-md p-4 cursor-pointer hover:bg-gray-50 flex items-center gap-2"
              >
                <FaUpload className="w-4 h-4" />
                {isUploadingImage ? "Uploading..." : "Upload Image"}
              </label>
              {formData.categoryIcon && (
                <img
                  src={formData.categoryIcon}
                  alt="Category icon"
                  className="w-12 h-12 rounded-full object-cover"
                />
              )}
            </div>
          </div>

          {/* Category Types */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Types *
            </label>
            {categoryTypes.map((type, index) => (
              <div key={index} className="flex items-center gap-2 mb-2">
                <input
                  type="text"
                  value={type.name}
                  onChange={(e) =>
                    handleCategoryTypeChange(index, e.target.value)
                  }
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
                  placeholder="Enter category type"
                />
                {categoryTypes.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeCategoryType(index)}
                    className="text-red-600 hover:text-red-800 p-1"
                  >
                    <FaTimes className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              onClick={addCategoryType}
              className="text-secondary hover:text-secondary/80 flex items-center gap-1 text-sm"
            >
              <FaPlus className="w-3 h-3" />
              Add Category Type
            </button>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || isUploadingImage}
              className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Creating..." : "Create Category"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCategoryModal;
