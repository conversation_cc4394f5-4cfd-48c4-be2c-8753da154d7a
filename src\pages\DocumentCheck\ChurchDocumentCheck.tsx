import { useState, useEffect } from "react";
import axios from "axios";
import SelfEmployed from "../../components/modals/SelfEmployed";
import BVNVerificationModal from "../../components/modals/BVNVerificationModal";
import CreditLimitModal from "../../components/modals/CreditLimitModal";
import Employed from "../../components/modals/Employed";
import toast from "react-hot-toast";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import Student from "../../components/modals/Student";
// import { AllBanksModal } from "../components/modals/AllBanksModal";
import { Preloader } from "../../components/elements/Preloader";
import DisapproveModal from "../../components/DisapproveModal";
import Pagination from "../../components/Pagination";
import { IoSearchOutline } from "react-icons/io5";
import { <PERSON>aPhone, FaRegCopy, FaWhatsapp } from "react-icons/fa6";

const formatDate = (dateStr: string | undefined): string => {
  if (!dateStr || typeof dateStr !== "string") {
    return "Not specified";
  }

  try {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    const formattedDate = date.toLocaleDateString("en-US", options);

    return formattedDate;
  } catch (e) {
    console.error(`formatDate: Error formatting date "${dateStr}":`, e);
    return dateStr;
  }
};

export const ChurchDocumentCheck = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [bvnModalOpen, setBvnModalOpen] = useState(false);
  const [studentModalOpen, setStudentModalOpen] = useState(false);
  const [selfEmployedModalOpen, setSelfEmployedModalOpen] = useState(false);
  const [filter, setFilter] = useState("all");
  const [selectedBvn, setSelectedBvn] = useState<any>({});
  const [selectedInfo, setSelectedInfo] = useState<any>({});
  const [creditLimit, setCreditLimit] = useState("");
  const [action1, setAction1] = useState(false);
  const [disapproveMsg, setDisapproveMsg] = useState("");
  const [action2, setAction2] = useState("");
  const [showMsg, setShowMsg] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [employedModalOpen, setEmployedModalOpen] = useState(false);
  const [creditLimitModalOpen, setCreditLimitModalOpen] = useState(false);
  const [churchUsers, setChurchUsers] = useState<any[]>([]);
  const [allAdmins, setAllAdmins] = useState<any[]>([]);
  const [bankStatement, setBankStatement] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = churchUsers.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(churchUsers.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Search functionality
  const filteredUsers = currentItems.filter((user) => {
    const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
    const email = user.email?.toLowerCase() || "";
    const phone = user.phoneNumber?.toLowerCase() || "";
    const search = searchTerm.toLowerCase();

    return (
      fullName.includes(search) ||
      email.includes(search) ||
      phone.includes(search)
    );
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        await getAllChurchUsers();
        await getAllAdmins();
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getAllChurchUsers = async () => {
    setIsLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getChurchUsers`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setChurchUsers(
        res?.data?.users?.filter(
          (item: any) =>
            item?.categoryType === "church" &&
            item?.adminVerification === "stage2"
        )
      );
    } catch (error) {
      // toast.error("Error fetching church data");
    } finally {
      setIsLoading(false);
    }
  };

  const getAllAdmins = async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllAdmins`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setAllAdmins(res?.data?.admins || []);
    } catch (error) {
      console.error("Error fetching admins:", error);
    }
  };

  // This function is kept for future use when occupation details need to be displayed
  // Currently not used since all users are "Civil Servant" but maintained for consistency with other document check pages
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleJobClick = (
    details: string,
    occupation: any,
    bankStatement: string
  ) => {
    setSelectedInfo(details);
    setBankStatement(bankStatement);
    if (occupation === "Self-Employed") {
      setSelfEmployedModalOpen(true);
    } else if (occupation === "Employed") {
      setEmployedModalOpen(true);
    } else {
      setStudentModalOpen(true);
    }
  };

  const handleBvnClick = (bvn: any) => {
    setSelectedBvn(bvn);
    setBvnModalOpen(true);
  };

  const handleCreditLimitClick = (creditLimit: string) => {
    setCreditLimit(creditLimit);
    setCreditLimitModalOpen(true);
  };

  const handleAction1 = async (e: any, userId: string) => {
    const payload = {
      adminVerification: "stage3",
    };

    try {
      const res = await axios.patch(
        `${process.env.REACT_APP_API_URL}/update-admin-verification/${userId}`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      await getAllChurchUsers();
      toast.success(res?.data?.message || "Account approved!");
    } catch (error: any) {
      console.error(error);
      toast.error(error.message || "Approval failed");
    }
  };

  const handleAction2 = async (e: any, userId: string, action: string) => {
    const payload = {
      adminVerification: action,
    };

    try {
      const res = await axios.patch(
        `${process.env.REACT_APP_API_URL}/update-admin-verification/${userId}`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      await getAllChurchUsers();
      toast.success(res?.data?.message || "Action completed!");
    } catch (error: any) {
      console.error(error);
      // toast.error(error.message || "Action failed");
    }
  };

  const handleDisapproveClick = (msg: string) => {
    setDisapproveMsg(msg);
    setShowMsg(true);
  };

  const handleConfirmation = async (e: any, userId: string) => {
    const payload = {
      documentCheck: true,
    };

    try {
      const res = await axios.patch(
        `${process.env.REACT_APP_API_URL}/update-document-check/${userId}`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      await getAllChurchUsers();
      toast.success(res?.data?.message || "Account confirmed!");
    } catch (error: any) {
      console.error(error);
      toast.error(error.message || "Confirmation failed");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard!");
  };

  const openWhatsApp = (phoneNumber: string) => {
    const cleanNumber = phoneNumber.replace(/\D/g, "");
    const whatsappUrl = `https://wa.me/${cleanNumber}`;
    window.open(whatsappUrl, "_blank");
  };

  const makePhoneCall = (phoneNumber: string) => {
    window.location.href = `tel:${phoneNumber}`;
  };

  if (isLoading) {
    return <Preloader />;
  }

  return (
    <main>
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-bold text-lg">Church Document Check</h2>

        {/* Search Bar */}
        <div className="relative">
          <IoSearchOutline className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
      </div>

      <section className="bg-white rounded-md shadow-md p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Preloader />
          </div>
        ) : (
          <table className="w-full text-left border-collapse">
            <thead>
              <tr>
                <th className="p-2">S/N</th>
                <th className="p-2">Name</th>
                <th className="p-2">Email</th>
                <th className="p-2 text-nowrap">Phone Number</th>
                <th className="p-2 text-nowrap">Contact</th>
                <th className="p-2">BVN</th>
                <th className="p-2">Occupation</th>
                <th className="p-2 text-nowrap">Date Created</th>
                <th className="p-2">Admin Officer</th>
                <th className="p-2 text-nowrap">Action 1</th>
                <th className="p-2 text-nowrap">Action 2 MA</th>
                <th className="p-2 text-nowrap">Action 3 VA</th>
                <th className="p-2 text-nowrap">Action 4</th>
                <th className="p-2">Confirmation</th>
                <th className="p-2">Bank Statement</th>
                <th className="p-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user: any, index: number) => (
                  <tr key={user._id} className="border-b hover:bg-gray-50">
                    <td className="p-2">{indexOfFirstItem + index + 1}</td>
                    <td className="p-2">{`${user.firstName} ${user.lastName}`}</td>
                    <td className="p-2">{user.email}</td>
                    <td className="p-2">{user.phoneNumber}</td>
                    <td className="p-2">
                      <div className="flex gap-2">
                        <button
                          onClick={() => copyToClipboard(user.phoneNumber)}
                          className="text-blue-500 hover:text-blue-700"
                          title="Copy phone number"
                        >
                          <FaRegCopy />
                        </button>
                        <button
                          onClick={() => openWhatsApp(user.phoneNumber)}
                          className="text-green-500 hover:text-green-700"
                          title="Open WhatsApp"
                        >
                          <FaWhatsapp />
                        </button>
                        <button
                          onClick={() => makePhoneCall(user.phoneNumber)}
                          className="text-blue-500 hover:text-blue-700"
                          title="Make phone call"
                        >
                          <FaPhone />
                        </button>
                      </div>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={() => handleBvnClick(user.bvn)}
                        className="text-blue-500 hover:underline"
                      >
                        {user.bvn}
                      </button>
                    </td>
                    <td className="p-2">Civil Servant</td>
                    <td className="p-2 text-nowrap">
                      {formatDate(user.createdAt)}
                    </td>
                    <td className="p-2 text-nowrap">
                      {(() => {
                        const admin = allAdmins?.find(
                          (item: any) => item?.id === user.adminOfficer
                        );

                        return (
                          <span>
                            {admin
                              ? `${admin.lastName} ${admin.firstName}`
                              : "-- --"}
                          </span>
                        );
                      })()}
                    </td>
                    <td className="p-2">
                      <button
                        onClick={(e) => handleAction1(e, user._id)}
                        className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                      >
                        Approve
                      </button>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={(e) => handleAction2(e, user._id, "stage4")}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                      >
                        MA
                      </button>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={(e) => handleAction2(e, user._id, "stage5")}
                        className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600"
                      >
                        VA
                      </button>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={() => handleDisapproveClick("Disapproved")}
                        className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
                      >
                        Disapprove
                      </button>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={(e) => handleConfirmation(e, user._id)}
                        className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                      >
                        Confirm
                      </button>
                    </td>
                    <td className="p-2">
                      <button
                        onClick={() => handleCreditLimitClick(user.creditLimit)}
                        className="text-blue-500 hover:underline"
                      >
                        View
                      </button>
                    </td>
                    <td className="p-2">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          user.adminVerification === "stage3"
                            ? "bg-green-100 text-green-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {user.adminVerification}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={16} className="p-8 text-center text-gray-500">
                    No church users found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}

        {/* Pagination */}
        {churchUsers.length > itemsPerPage && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
              length={churchUsers.length}
              handlePagination={handlePageChange}
              prevPage={handlePrevPage}
              nextPage={handleNextPage}
            />
          </div>
        )}
      </section>

      {/* Modals */}
      {bvnModalOpen && (
        <BVNVerificationModal
          setBvnModalOpen={setBvnModalOpen}
          selectedBvn={selectedBvn}
        />
      )}
      {selfEmployedModalOpen && (
        <SelfEmployed
          setSelfEmployedModalOpen={setSelfEmployedModalOpen}
          selectedInfo={selectedInfo}
          bankStatement={bankStatement}
        />
      )}
      {employedModalOpen && (
        <Employed
          setEmployedModalOpen={setEmployedModalOpen}
          selectedInfo={selectedInfo}
          bankStatement={bankStatement}
        />
      )}
      {studentModalOpen && (
        <Student
          setStudentModalOpen={setStudentModalOpen}
          selectedInfo={selectedInfo}
          bankStatement={bankStatement}
        />
      )}
      {creditLimitModalOpen && (
        <CreditLimitModal
          setCreditLimitModalOpen={setCreditLimitModalOpen}
          creditLimit={creditLimit}
        />
      )}
      {showMsg && (
        <DisapproveModal
          setShowMsg={setShowMsg}
          disapproveMsg={disapproveMsg}
        />
      )}
    </main>
  );
};
