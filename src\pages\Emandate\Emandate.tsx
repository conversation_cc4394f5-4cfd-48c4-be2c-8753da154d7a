import axios from "axios";
import { useEffect, useState } from "react";
import { EMandateAccts } from "../../components/modals/EmandateAccts";
import { RootState } from "../../redux/store";
import { useAppSelector } from "../../redux/hooks";
import { Preloader } from "../../components/elements/Preloader";
import { IoSearchOutline } from "react-icons/io5";
import { IoIosArrowRoundBack, IoIosArrowRoundForward } from "react-icons/io";

export const Emandate = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [allEmandate, setAllEmandate] = useState<[]>([]);
  const [showEmandateModal, setShowEmandateModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchedUser, setSearchedUser] = useState<any>([]);
  const [searchResults, setSearchResults] = useState([]);
  const [acctNameClick, setAcctNameClick] = useState({
    acctName: "",
    accounts: [],
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState<any>([]);

  const handleViewMore = (acctName: string) => {
    setAcctNameClick((prev) => ({
      ...prev,
      acctName: acctName,
    }));

    const acctArray = allEmandate.filter(
      (acct: any) => acct.account_name === acctName
    );
    setAcctNameClick((prev) => ({
      ...prev,
      accounts: acctArray,
    }));

    setShowEmandateModal((prev) => !prev);
  };

  const fetchMandates = async (pageNo: number | null = null) => {
    setIsLoading(true);
    // currentPage state is used on initial load up. pageNo is used when the button is clicked.
    const pageNumber = pageNo ? pageNo : currentPage;
    try {
      const res = await axios.get(
        `${
          process.env.REACT_APP_API_URL
        }/getActiveMandates?limit=${5000}&page=${pageNumber}
        `,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      // Filters the allEmandate array to remove duplicates based on the account_name property.
      const filteredEmandate = res.data?.filter(
        (account: any, index: number, self: any) =>
          index ===
          self.findIndex((t: any) => t.account_name === account.account_name)
      );

      setAllEmandate(filteredEmandate.reverse());
      // Set the totalResults state with unique results to avoid duplicates
      setTotalResults((prev: any) => {
        const uniqueResults = [
          ...prev,
          ...filteredEmandate.filter(
            (newItem: any) =>
              !prev.some((existingItem: any) => existingItem.id === newItem.id)
          ),
        ];
        return uniqueResults;
      });
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMandates();
  }, []);

  // Pagination
  const prevPage = () => {
    if (currentPage !== 1) {
      setSearchedUser("");
      setCurrentPage((prev) => prev - 1);
      fetchMandates(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (allEmandate.length !== 0) {
      setSearchedUser("");
      setCurrentPage((prev) => prev + 1);
      fetchMandates(currentPage + 1);
    }
  };

  const searchForUsersWithAccountName = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const searchResult =
        totalResults?.filter((user: any) =>
          user.account_name?.toLowerCase().includes(value.toLowerCase())
        ) || [];
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  return (
    <main>
      <div className="bg-white p-3 mt-3 rounded-md flex items-center justify-between">
        <h1 className="font-bold text-lg">All E-Mandate List</h1>
        <div className="relative md:w-[30rem] w-fit">
          <IoSearchOutline className="w-6 h-6 absolute top-[0.6rem] left-2 text-gray-300" />
          <input
            type="search"
            name="searchedUser"
            id="searchedUser"
            value={searchedUser}
            onChange={(e) => searchForUsersWithAccountName(e.target.value)}
            placeholder="Search user using account name"
            className="border p-2 rounded-md indent-7 w-full"
            disabled={allEmandate.length === 0}
          />
        </div>
      </div>
      <section>
        <table className="w-full my-3">
          <thead>
            <tr className="bg-gray-50 font-bold md:text-base text-sm h-12 text-left">
              <th className="pl-3">SN</th>
              <th className="pl-3">Client Account Name</th>
              <th className="pl-3"></th>
            </tr>
          </thead>
          <tbody className="md:text-base text-sm">
            {isLoading ? (
              <tr>
                <td colSpan={3} className="text-center p-5">
                  <Preloader />
                </td>
              </tr>
            ) : searchedUser.length > 0 ? (
              searchResults && searchResults.length > 0 ? (
                searchResults.map((item: any, index: number) => (
                  <tr key={index} className="border-b-2">
                    <td className="p-3">{index + 1}</td>
                    <td className="p-3">{item.account_name}</td>
                    <td className="p-3">
                      <button
                        type="button"
                        className="bg-secondary text-white p-2 rounded-md text-sm"
                        onClick={() => handleViewMore(item.account_name)}
                      >
                        View More
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={10} className="text-center text-secondary p-3">
                    User with account name not found
                  </td>
                </tr>
              )
            ) : !isLoading && allEmandate.length > 0 ? (
              allEmandate.map((item: any, index: number) => (
                <tr key={index} className="border-b-2">
                  <td className="p-3">
                    {totalResults.findIndex(
                      (result: any) => result.id === item.id
                    ) + 1}
                  </td>
                  <td className="p-3">{item.account_name}</td>
                  <td className="p-3">
                    <button
                      type="button"
                      className="bg-secondary text-white p-2 rounded-md text-sm"
                      onClick={() => handleViewMore(item.account_name)}
                    >
                      View More
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={3} className="text-center text-secondary p-5">
                  No Mandates found!
                </td>
              </tr>
            )}
          </tbody>
        </table>
        <section className="p-3 my-5">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              Shown {totalResults.length} results
            </p>
            <div className="flex gap-2 items-center">
              <button
                onClick={() => prevPage()}
                className="bg-white p-1 rounded-full border-secondary border text-sm"
              >
                <IoIosArrowRoundBack className="w-4 h-4" />
              </button>
              <span className="text-sm">Page {currentPage}</span>
              <button
                onClick={() => nextPage()}
                className="bg-white p-1 rounded-full border-secondary border text-sm"
              >
                <IoIosArrowRoundForward className="w-4 h-4" />
              </button>
            </div>
          </div>
        </section>
      </section>
      {showEmandateModal && (
        <EMandateAccts
          setShowEmandateModal={setShowEmandateModal}
          acctNameClick={acctNameClick}
        />
      )}
    </main>
  );
};
