import { createSlice } from "@reduxjs/toolkit";
import { fetchPrivateOrgDetails } from "../thunk";

const initialState = {
  privateOrgs: [],
  status: "idle",
  error: "",
};

const privateOrgSlice = createSlice({
  name: "privateOrgs",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPrivateOrgDetails.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(fetchPrivateOrgDetails.fulfilled, (state, action) => {
        state.status = "success";
        const data = action.payload;
        state.privateOrgs = data.map((item: any) => ({
          name: item.category,
          orgId: item._id,
          departments: item.categoryType,
        }))
        state.error = "nil";
      })
      .addCase(fetchPrivateOrgDetails.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      });
  },
});

export default privateOrgSlice.reducer;
