import { useEffect, useState } from "react";
import ActiveComponent from "../../components/stateAndBank/ActiveComponent";
import InactiveComponent from "../../components/stateAndBank/InactiveComponent";
import CreateModal from "../../components/stateAndBank/CreateModal";
import axios from "axios";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";

const data = [
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "***********",
    bvn: "***********",
    creditScoreBal: "N23,000.00",
    creditScore: "N23,000.00",
    openPassword: "Ahb@12354",
  },
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "***********",
    bvn: "***********",
    creditScoreBal: "N23,000.00",
    creditScore: "N23,000.00",
    openPassword: "Ahb@12354",
  },
  {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "***********",
    bvn: "***********",
    creditScoreBal: "N23,000.00",
    creditScore: "N23,000.00",
    openPassword: "Ahb@12354",
  },
  {
    name: "Ahmadu Bello",
    email: "<EMAIL>",
    phone: "***********",
    bvn: "***********",
    creditScoreBal: "N23,000.00",
    creditScore: "N23,000.00",
    openPassword: "Ahb@12354",
  },
];

const Index = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [allBanks, setAllBanks] = useState<any>([]);
  const [selectedSubData, setSelectedSubdata] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [subCategoryId, setSubCategoryId] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [createMinistry, setCreateMinistry] = useState(false);
  const [selectedData, setSelectedData] = useState<any>({
    name: "",
    logo: "",
    branches: [],
  });

  const fetchCategories = async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "micro finance banks"
      );
      setAllBanks(data);
    } catch (error) {
      console.error("An error occurred: ", error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <>
      {selectedSubData.length > 0 ? (
        <ActiveComponent
          selectedData={selectedData}
          selectedSubData={selectedSubData}
          setSelectedSubdata={setSelectedSubdata}
          data={data}
          categoryId={categoryId}
          subCategoryId={subCategoryId}
          setShowCreateModal={setShowCreateModal}
          path={"mfbs"}
          setCreateMinistry={setCreateMinistry}
        />
      ) : (
        <InactiveComponent
          setSelectedData={setSelectedData}
          setSelectedSubdata={setSelectedSubdata}
          selectedData={selectedData}
          data={allBanks}
          setCategoryId={setCategoryId}
          setSubCategoryId={setSubCategoryId}
          setShowCreateModal={setShowCreateModal}
          path={"mfbs"}
          createMinistry={createMinistry}
          setCreateMinistry={setCreateMinistry}
        />
      )}
      {showCreateModal && (
        <CreateModal
          closeModal={() => {
            setShowCreateModal(false);
            fetchCategories();
          }}
          path={"mfbs"}
        />
      )}
    </>
  );
};

export default Index;
