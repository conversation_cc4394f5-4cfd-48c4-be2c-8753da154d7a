import React, { useState, useEffect } from "react";
import { FaPlus, FaArrowLeft } from "react-icons/fa";
import toast from "react-hot-toast";
import CreateCategoryModal from "./CreateCategoryModal";
import { useAppSelector } from "../../redux/hooks";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  employmentStatus: string;
  categoryType: string;
  bvn: string;
  isComplete: string;
  adminVerification: string;
  documentCheck: boolean;
  createdAt: string;
  ministryId?: string;
  stateGovernmentData?: {
    ministryId: string;
  };
}

interface UserDataResponse {
  status: string;
  currentPage: number;
  totalPages: number;
  totalCustomers: number;
  data: User[];
}

const UserCategory: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [selectedType, setSelectedType] = useState<CategoryType | null>(null);
  const [userData, setUserData] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const { token } = useAppSelector((store) => store.auth);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);

  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Remove duplicates based on category name (case-insensitive)
        const uniqueCategories =
          data.categories?.filter(
            (category: Category, index: number, self: Category[]) =>
              index ===
              self.findIndex(
                (c) =>
                  c.category.toLowerCase() === category.category.toLowerCase()
              )
          ) || [];
        setCategories(uniqueCategories);
      } else {
        toast("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast("Error fetching categories");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleCreateCategory = () => {
    setShowCreateModal(true);
  };

  const handleCategoryCreated = () => {
    fetchCategories();
    setShowCreateModal(false);
  };

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category);
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setSelectedType(null);
    setUserData([]);
  };

  const handleBackToTypes = () => {
    setSelectedType(null);
    setUserData([]);
  };

  const fetchUserData = async (
    categoryType: string,
    filterValue: string,
    filterType: "employmentStatus" | "ministryId",
    page: number = 1
  ) => {
    // Prevent multiple simultaneous calls
    if (isLoadingUsers) {
      return;
    }

    setIsLoadingUsers(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/getCustomersByCategoryType?categoryType=${categoryType}&page=${page}&limit=10`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data: UserDataResponse = await response.json();

        // Filter based on the filter type
        let filteredData: User[] = [];

        if (filterType === "employmentStatus") {
          // Filter by employment status for individual category
          filteredData = data.data.filter(
            (user) =>
              user.employmentStatus.toLowerCase() === filterValue.toLowerCase()
          );
        } else if (filterType === "ministryId") {
          // Filter by ministry ID for state government category
          filteredData = data.data.filter(
            (user) =>
              user.stateGovernmentData?.ministryId === filterValue ||
              user.ministryId === filterValue
          );
        }

        setUserData(filteredData);
        setCurrentPage(data.currentPage);
        setTotalPages(data.totalPages);
        setTotalUsers(filteredData.length);
      } else {
        if (response.status === 401) {
          toast.error("Unauthorized access. Please login again.");
        } else {
          toast.error("Failed to fetch user data");
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      toast.error("Error fetching user data");
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleTypeClick = (type: CategoryType) => {
    // Prevent rapid clicks
    if (selectedCategory && !isLoadingUsers) {
      setSelectedType(type);

      const categoryType = selectedCategory.category.toLowerCase();

      if (categoryType === "individual") {
        // For individual category, filter by employment status
        const employmentStatusMap: { [key: string]: string } = {
          employed: "employed",
          student: "student",
          "self-employed": "selfEmployed",
        };

        const employmentStatus =
          employmentStatusMap[type.name.toLowerCase()] ||
          type.name.toLowerCase();

        fetchUserData(categoryType, employmentStatus, "employmentStatus");
      } else if (
        categoryType === "oyo state government" ||
        categoryType === "stategovernment"
      ) {
        // For state government category, filter by ministry ID
        fetchUserData("stateGovernment", type._id, "ministryId");
      } else {
        // For other categories, you can add more logic here
        // For now, default to employment status filtering
        fetchUserData(
          categoryType,
          type.name.toLowerCase(),
          "employmentStatus"
        );
      }
    }
  };

  // Helper function to get first two letters of category name
  const getCategoryInitials = (categoryName: string) => {
    return categoryName.substring(0, 2).toUpperCase();
  };

  // Show user data table if a type is selected
  if (selectedType && selectedCategory) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-white rounded-lg shadow-md">
          {/* Header */}
          <div className="flex items-center p-6 border-b">
            <button
              onClick={handleBackToTypes}
              className="mr-4 text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-100"
            >
              <FaArrowLeft className="w-4 h-4" />
            </button>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center text-sm font-semibold">
                {getCategoryInitials(selectedCategory.category)}
              </div>
              <h1 className="text-2xl font-bold text-gray-800">
                {selectedCategory.category} - {selectedType.name} Users
              </h1>
            </div>
          </div>

          {/* User Data Table */}
          <div className="p-6">
            {isLoadingUsers ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="text-left p-4 font-semibold text-gray-700">
                        S/N
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Name
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Email
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Phone
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        BVN
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Status
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Verification
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Date Joined
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {userData.length > 0 ? (
                      userData.map((user, index) => (
                        <tr
                          key={user._id}
                          className="border-b hover:bg-gray-50 transition-colors"
                        >
                          <td className="p-4 text-gray-600">{index + 1}</td>
                          <td className="p-4 font-medium text-gray-800">
                            {user.firstName} {user.lastName}
                          </td>
                          <td className="p-4 text-gray-600">{user.email}</td>
                          <td className="p-4 text-gray-600">
                            {user.phoneNumber}
                          </td>
                          <td className="p-4 text-gray-600">
                            {user.bvn || "---"}
                          </td>
                          <td className="p-4">
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                user.isComplete === "complete"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {user.isComplete}
                            </span>
                          </td>
                          <td className="p-4">
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                user.documentCheck
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {user.documentCheck ? "Verified" : "Pending"}
                            </span>
                          </td>
                          <td className="p-4 text-gray-600">
                            {new Date(user.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              }
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={8}
                          className="text-center p-8 text-gray-500"
                        >
                          No users found for {selectedType.name}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {/* Summary */}
            {userData.length > 0 && (
              <div className="mt-4 text-sm text-gray-600">
                Showing {userData.length} {selectedType.name.toLowerCase()} user
                {userData.length !== 1 ? "s" : ""}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show category types view if a category is selected
  if (selectedCategory) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-white rounded-lg shadow-md">
          {/* Header */}
          <div className="flex items-center p-6 border-b">
            <button
              onClick={handleBackToCategories}
              className="mr-4 text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-100"
            >
              <FaArrowLeft className="w-4 h-4" />
            </button>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center text-sm font-semibold">
                {getCategoryInitials(selectedCategory.category)}
              </div>
              <h1 className="text-2xl font-bold text-gray-800">
                {selectedCategory.category} Types
              </h1>
            </div>
          </div>

          {/* Category Types List */}
          <div className="p-6">
            <div className="grid gap-3">
              {selectedCategory.categoryType.map((type, index) => (
                <div
                  key={type._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => handleTypeClick(type)}
                >
                  <div className="flex items-center gap-3">
                    <span className="bg-secondary text-white text-sm px-3 py-1 rounded-full">
                      {index + 1}
                    </span>
                    <span className="font-medium text-gray-800 hover:text-secondary">
                      {type.name}
                    </span>
                  </div>
                  <span className="text-gray-400 text-sm">
                    Click to view users →
                  </span>
                </div>
              ))}
              {selectedCategory.categoryType.length === 0 && (
                <div className="text-center p-8 text-gray-500">
                  No types found for this category
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-md">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h1 className="text-2xl font-bold text-gray-800">User Categories</h1>
          <button
            onClick={handleCreateCategory}
            className="bg-secondary text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-secondary/90 transition-colors"
          >
            <FaPlus className="w-4 h-4" />
            Create Category
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="text-left p-4 font-semibold text-gray-700">
                      S/N
                    </th>
                    <th className="text-left p-4 font-semibold text-gray-700">
                      Category
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {categories.length > 0 ? (
                    categories.map((category, index) => (
                      <tr
                        key={category._id}
                        className="border-b hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => handleCategoryClick(category)}
                      >
                        <td className="p-4 text-gray-600">{index + 1}</td>
                        <td className="p-4 font-medium text-gray-800 hover:text-secondary">
                          <div className="flex items-center justify-between">
                            <span>{category.category}</span>
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                              {category.categoryType.length} type
                              {category.categoryType.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={2} className="text-center p-8 text-gray-500">
                        No categories found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Category Modal */}
      {showCreateModal && (
        <CreateCategoryModal
          onClose={() => setShowCreateModal(false)}
          onCategoryCreated={handleCategoryCreated}
        />
      )}
    </div>
  );
};

export default UserCategory;
