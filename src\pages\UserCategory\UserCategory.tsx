import React, { useState, useEffect } from "react";
import { FaPlus, FaArrowLeft } from "react-icons/fa";
import toast from "react-hot-toast";
import CreateCategoryModal from "./CreateCategoryModal";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

const UserCategory: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );

  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Remove duplicates based on category name (case-insensitive)
        const uniqueCategories =
          data.categories?.filter(
            (category: Category, index: number, self: Category[]) =>
              index ===
              self.findIndex(
                (c) =>
                  c.category.toLowerCase() === category.category.toLowerCase()
              )
          ) || [];
        setCategories(uniqueCategories);
      } else {
        toast("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast("Error fetching categories");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleCreateCategory = () => {
    setShowCreateModal(true);
  };

  const handleCategoryCreated = () => {
    fetchCategories();
    setShowCreateModal(false);
  };

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category);
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
  };

  // Helper function to get first two letters of category name
  const getCategoryInitials = (categoryName: string) => {
    return categoryName.substring(0, 2).toUpperCase();
  };

  // Helper function to generate fallback image URL
  const getFallbackImageUrl = (categoryName: string, size: number = 48) => {
    const initials = getCategoryInitials(categoryName);
    return `https://via.placeholder.com/${size}/e5e7eb/6b7280?text=${initials}`;
  };

  // Helper function to check if image URL is valid
  const isValidImageUrl = (url: string) => {
    if (
      !url ||
      url.trim() === "" ||
      url === "xyz.com" ||
      url.includes("placeholder") ||
      url.length < 10
    ) {
      return false;
    }
    try {
      const urlObj = new URL(url);
      // Check if it's a valid HTTP/HTTPS URL
      return urlObj.protocol === "http:" || urlObj.protocol === "https:";
    } catch {
      return false;
    }
  };

  // Helper function to get image source with fallback
  const getImageSrc = (
    categoryIcon: string,
    categoryName: string,
    size: number = 48
  ) => {
    return isValidImageUrl(categoryIcon)
      ? categoryIcon
      : getFallbackImageUrl(categoryName, size);
  };

  // Show category types view if a category is selected
  if (selectedCategory) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-white rounded-lg shadow-md">
          {/* Header */}
          <div className="flex items-center p-6 border-b">
            <button
              onClick={handleBackToCategories}
              className="mr-4 text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-100"
            >
              <FaArrowLeft className="w-4 h-4" />
            </button>
            <div className="flex items-center gap-3">
              <img
                src={getImageSrc(
                  selectedCategory.categoryIcon,
                  selectedCategory.category,
                  32
                )}
                alt={selectedCategory.category}
                className="w-8 h-8 rounded-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = getFallbackImageUrl(
                    selectedCategory.category,
                    32
                  );
                }}
              />
              <h1 className="text-2xl font-bold text-gray-800">
                {selectedCategory.category} Types
              </h1>
            </div>
          </div>

          {/* Category Types List */}
          <div className="p-6">
            <div className="grid gap-3">
              {selectedCategory.categoryType.map((type, index) => (
                <div
                  key={type._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span className="bg-secondary text-white text-sm px-3 py-1 rounded-full">
                      {index + 1}
                    </span>
                    <span className="font-medium text-gray-800">
                      {type.name}
                    </span>
                  </div>
                </div>
              ))}
              {selectedCategory.categoryType.length === 0 && (
                <div className="text-center p-8 text-gray-500">
                  No types found for this category
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-md">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h1 className="text-2xl font-bold text-gray-800">User Categories</h1>
          <button
            onClick={handleCreateCategory}
            className="bg-secondary text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-secondary/90 transition-colors"
          >
            <FaPlus className="w-4 h-4" />
            Create Category
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="text-left p-4 font-semibold text-gray-700">
                      S/N
                    </th>
                    <th className="text-left p-4 font-semibold text-gray-700">
                      Category
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {categories.length > 0 ? (
                    categories.map((category, index) => (
                      <tr
                        key={category._id}
                        className="border-b hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => handleCategoryClick(category)}
                      >
                        <td className="p-4 text-gray-600">{index + 1}</td>
                        <td className="p-4 font-medium text-gray-800 hover:text-secondary">
                          <div className="flex items-center justify-between">
                            <span>{category.category}</span>
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                              {category.categoryType.length} type
                              {category.categoryType.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={2} className="text-center p-8 text-gray-500">
                        No categories found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Category Modal */}
      {showCreateModal && (
        <CreateCategoryModal
          onClose={() => setShowCreateModal(false)}
          onCategoryCreated={handleCategoryCreated}
        />
      )}
    </div>
  );
};

export default UserCategory;
