{"name": "way-up", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.2.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.81", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "axios": "^1.6.7", "chart.js": "^4.4.7", "date-fns": "^3.3.1", "dotenv": "^16.4.7", "gsap": "^3.12.5", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-multi-select-component": "^4.3.4", "react-redux": "^9.1.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "socket.io-client": "^4.8.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.1"}}