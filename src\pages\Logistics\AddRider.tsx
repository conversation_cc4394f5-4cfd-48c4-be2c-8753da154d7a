import { useState, ChangeEvent, FormEvent } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { createDispatcher } from "../../redux/thunk";
import toast from "react-hot-toast";
import { FaArrowLeft } from "react-icons/fa";

interface Errors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  phoneNumber?: string;
}

const AddRider = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { createDispatcherStatus } = useAppSelector((store) => store.logistics);

  const [errors, setErrors] = useState<Errors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [inputValue, setInputValue] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  });

  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (value.trim() !== "") {
      setErrors({
        ...errors,
        [name]: undefined,
      });
    }
    setInputValue((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors: Errors = {};

    // Validate first name
    if (!inputValue.firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    }

    // Validate last name
    if (!inputValue.lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    }

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!inputValue.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!emailRegex.test(inputValue.email)) {
      newErrors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Validate phone number
    const phoneRegex = /^[0-9]{11}$/;
    if (!inputValue.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
      isValid = false;
    } else if (!phoneRegex.test(inputValue.phoneNumber)) {
      newErrors.phoneNumber = "Please enter a valid 11-digit phone number";
      isValid = false;
    }

    // Validate password
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/;
    if (!inputValue.password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (!passwordRegex.test(inputValue.password)) {
      newErrors.password =
        "Password must be at least 8 characters and include uppercase, lowercase, number, and special character";
      isValid = false;
    }

    // Validate confirm password
    if (inputValue.password !== inputValue.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleCreateRider = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (validateForm()) {
      const payload = {
        firstName: inputValue.firstName,
        lastName: inputValue.lastName,
        email: inputValue.email,
        phoneNumber: inputValue.phoneNumber,
        password: inputValue.password,
      };

      try {
        const resultAction = await dispatch(createDispatcher(payload));
        if (createDispatcher.fulfilled.match(resultAction)) {
          toast.success("Rider account created successfully");
          // Force a refresh of the logistics page by using window.location instead of navigate
          window.location.href = "/logistics";
        } else if (createDispatcher.rejected.match(resultAction)) {
          toast.error(
            (resultAction.payload as string) || "Failed to create rider account"
          );
        }
      } catch (error) {
        toast.error("An error occurred while creating the rider account");
      }
    }
  };

  return (
    <main className="w-full">
      <div className="bg-white rounded-md shadow-md p-6 mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate("/logistics")}
            className="text-secondary hover:text-green-700"
          >
            <FaArrowLeft size={20} />
          </button>
          <h1 className="text-xl font-semibold">Add New Rider</h1>
        </div>
      </div>

      <div className="bg-white rounded-md shadow-md p-6">
        <form onSubmit={handleCreateRider} className="max-w-2xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Name */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">First Name</label>
              <input
                type="text"
                name="firstName"
                value={inputValue.firstName}
                onChange={handleInputChange}
                placeholder="Enter first name"
                className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500"
              />
              {errors.firstName && (
                <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">Last Name</label>
              <input
                type="text"
                name="lastName"
                value={inputValue.lastName}
                onChange={handleInputChange}
                placeholder="Enter last name"
                className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500"
              />
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
              )}
            </div>

            {/* Email */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">Email</label>
              <input
                type="email"
                name="email"
                value={inputValue.email}
                onChange={handleInputChange}
                placeholder="Enter email address"
                className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500"
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            {/* Phone Number */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">Phone Number</label>
              <input
                type="text"
                name="phoneNumber"
                value={inputValue.phoneNumber}
                onChange={handleInputChange}
                placeholder="Enter phone number"
                className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.phoneNumber}
                </p>
              )}
            </div>

            {/* Password */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">Password</label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={inputValue.password}
                  onChange={handleInputChange}
                  placeholder="Enter password"
                  className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500 w-full"
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? "Hide" : "Show"}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div className="flex flex-col">
              <label className="text-sm font-medium pb-1">
                Confirm Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                name="confirmPassword"
                value={inputValue.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm password"
                className="bg-gray-100 outline-none text-sm p-3 rounded-md border border-gray-200 focus:border-green-500"
              />
              {errors.confirmPassword && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.confirmPassword}
                </p>
              )}
            </div>
          </div>

          <div className="mt-8 flex justify-center">
            <button
              type="submit"
              className="bg-secondary hover:bg-green-700 text-white py-3 px-8 rounded-md font-medium transition duration-300 flex items-center justify-center min-w-[200px]"
              disabled={createDispatcherStatus === "loading"}
            >
              {createDispatcherStatus === "loading" ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Creating...
                </span>
              ) : (
                "Create Rider Account"
              )}
            </button>
          </div>
        </form>
      </div>
    </main>
  );
};

export default AddRider;
