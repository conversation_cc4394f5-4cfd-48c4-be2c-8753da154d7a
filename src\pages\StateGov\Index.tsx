import { useEffect, useState } from "react";
import ActiveComponent from "../../components/stateAndBank/ActiveComponent";
import InactiveComponent from "../../components/stateAndBank/InactiveComponent";
import CreateModal from "../../components/stateAndBank/CreateModal";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import axios from "axios";

const Index = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [selectedSubData, setSelectedSubdata] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [allStates, setAllStates] = useState<any>([]);
  const [categoryId, setCategoryId] = useState("");
  const [subCategoryId, setSubCategoryId] = useState("");
  const [createMinistry, setCreateMinistry] = useState(false);
  const [selectedData, setSelectedData] = useState<any>({
    name: "",
    logo: "",
    ministries: [],
  });

  const fetchCategories = async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "oyo state government"
      );
      setAllStates(data);
    } catch (error) {
      console.error("An error occurred: ", error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <>
      {selectedSubData.length > 0 ? (
        <ActiveComponent
          selectedData={selectedData}
          setSelectedData={setSelectedData}
          selectedSubData={selectedSubData}
          setSelectedSubdata={setSelectedSubdata}
          setShowCreateModal={setShowCreateModal}
          categoryId={categoryId}
          subCategoryId={subCategoryId}
          path={"stateGovernment"}
          setCreateMinistry={setCreateMinistry}
        />
      ) : (
        <InactiveComponent
          setSelectedData={setSelectedData}
          setSelectedSubdata={setSelectedSubdata}
          selectedData={selectedData}
          data={allStates}
          setCategoryId={setCategoryId}
          setSubCategoryId={setSubCategoryId}
          setShowCreateModal={setShowCreateModal}
          path={"stateGovernment"}
          setCreateMinistry={setCreateMinistry}
          createMinistry={createMinistry}
        />
      )}
      {showCreateModal && (
        <CreateModal
          closeModal={() => {
            setShowCreateModal(false);
          }}
          path={"stateGovernment"}
          createMinistry={createMinistry}
          data={allStates}
          fetchCategories={fetchCategories}
        />
      )}
    </>
  );
};

export default Index;
