import { createSlice } from "@reduxjs/toolkit";
import { fetchStateGovernmentDetails } from "../thunk";

const initialState = {
  stateGovs: [],
  status: "idle",
  error: "",
};

const stateGovSlice = createSlice({
  name: "products",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchStateGovernmentDetails.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(fetchStateGovernmentDetails.fulfilled, (state, action) => {
        state.status = "success";
        const data = action.payload;
        state.stateGovs = data.map((item: any) => ({
          name: item.category,
          stateId: item._id,
          ministries: item.categoryType,
        }))
        state.error = "nil";
      })
      .addCase(fetchStateGovernmentDetails.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      });
  },
});

export default stateGovSlice.reducer;
