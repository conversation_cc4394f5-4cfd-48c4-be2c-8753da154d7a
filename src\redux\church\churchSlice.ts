import { createSlice } from "@reduxjs/toolkit";
import { fetchChurchDetails } from "../thunk";

const initialState = {
  churches: [],
  status: "idle",
  error: "",
};

const churchSlice = createSlice({
  name: "churches",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchChurchDetails.pending, (state) => {
        state.status = "loading";
        state.error = "nil";
      })
      .addCase(fetchChurchDetails.fulfilled, (state, action) => {
        state.status = "success";
        const data = action.payload;
        state.churches = data.map((item: any) => ({
          name: item.category,
          churchId: item._id,
          departments: item.categoryType,
        }))
        state.error = "nil";
      })
      .addCase(fetchChurchDetails.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error?.message || "Unknown error";
      });
  },
});

export default churchSlice.reducer;
