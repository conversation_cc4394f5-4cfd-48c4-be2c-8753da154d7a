import axios from "axios";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { RootState } from "../../redux/store";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { fetchStateGovernmentDetails } from "../../redux/thunk";

export const SalesAdminAssignment = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [roleLoading, setRoleLoading] = useState(false);
  const [salesAdmins, setSalesAdmins] = useState([]);
  const [stateFilter, setStateFilter] = useState("Oyo State Government");
  const { token } = useAppSelector((store: RootState) => store.auth);
  const { stateGovs } = useAppSelector((store: RootState) => store.stateGovs);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(fetchStateGovernmentDetails());
  }, []);

  // Filter the ministries based on the selected state
  // and map them to get the ministry names
  const ministriesByStateFilter = stateGovs.find(
    (state: { name: string; ministries: { name: string }[] }) =>
      state.name === stateFilter
  ) || { ministries: [] };

  // Get all the ministries for the selected state
  // If no state is selected, return an empty array
  const allPagesToSelect =
    stateFilter === ""
      ? []
      : ministriesByStateFilter?.ministries.map(
          (ministry: { name: string; _id: string }) => ministry
        ) || [];

  useEffect(() => {
    getAllAdmins();
  }, []);

  const getAllAdmins = async () => {
    setIsLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/get-admins-except-superadmin`
      );
      setIsLoading(false);
      const salesAdmin = res.data.filter(
        (admin: any) => admin.adminLevel === "sales-admin"
      );
      setSalesAdmins(salesAdmin);
    } catch (error) {
      console.error(error);
      setSalesAdmins([]);
      setIsLoading(false);
    }
  };

  const addRoleHandler = async ({
    id,
    roleValue,
    ministryId,
  }: {
    id: string;
    roleValue: string;
    ministryId: string;
  }) => {
    const payload = {
      staffId: id,
      role: roleValue,
      ministryId: ministryId,
    };
    setRoleLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/addAdminRole`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      getAllAdmins();
      toast.success(res.data.message);
    } catch (error) {
      console.error(error);
      toast.error("Unable to add admin role!");
    } finally {
      setRoleLoading(false);
    }
  };

  const removeRoleHandler = async (adminID: string, roleId: string) => {
    setRoleLoading(true);
    try {
      const res = await axios.delete(
        `${process.env.REACT_APP_API_URL}/delete-admin-role/${adminID}/${roleId}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success(res.data.message);
      getAllAdmins();
    } catch (error) {
      console.error(error);
      toast.error("Unable to remove admin role!");
    } finally {
      setRoleLoading(false);
    }
  };

  return (
    <main className="w-full">
      <div className="bg-white rounded-md shadow-md pb-6">
        <div
          className={`overflow-x-auto ${isLoading && "animate-pulse h-[50vh]"}`}
        >
          <div className="flex items-center justify-between p-6">
            <h1 className="text-base font-semibold">Sales Admin Assignment</h1>
            <div className="flex items-center">
              <label htmlFor="state" className="text-sm font-semibold mr-2">
                Filter by State:
              </label>
              <select
                name="state"
                id="state"
                className="border border-gray-300 rounded-md p-2 text-sm"
                onChange={(e) => setStateFilter(e.target.value)}
              >
                {stateGovs.map((state: any, index: number) => (
                  <option value={state.name} key={index}>
                    {state.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <h2 className="text-base font-semibold px-6">{stateFilter}</h2>
          <section className="w-full p-5 overflow-x-auto">
            <table
              className={`p-5 text-center w-[1200px] ${
                isLoading && "animate-pulse h-[60vh]"
              }`}
            >
              <thead className="bg-gray-50 font-bold h-12">
                <tr className="p-5">
                  <th className="text-sm text-nowrap px-2">S/N</th>
                  <th className="text-sm text-nowrap px-2">Name</th>
                  {allPagesToSelect &&
                    allPagesToSelect.map((ministry, index) => (
                      <th className="text-sm text-nowrap px-2" key={index}>
                        {ministry.name}
                      </th>
                    ))}
                </tr>
              </thead>
              <tbody className="px-4">
                {salesAdmins && salesAdmins.length > 0 ? (
                  salesAdmins.map((filteredAdmin: any, index) => {
                    const handleCheck = (value: string) => {
                      // Check if the admin has the role
                      // and return true or false
                      return filteredAdmin.adminRole.some(
                        (role: any) => role.role === value
                      );
                    };

                    return (
                      <tr className="border-b border-gray-300 py-3" key={index}>
                        <td className="text-secondary p-2 font-semibold">
                          {index + 1}
                        </td>
                        <td className="p-2 text-nowrap">
                          {filteredAdmin.lastName} {filteredAdmin.firstName}
                        </td>
                        {allPagesToSelect &&
                          allPagesToSelect.map((ministry, index) => (
                            <td className="p-2" key={index}>
                              <input
                                type="checkbox"
                                name="checkbox"
                                id="checkbox"
                                checked={handleCheck(ministry.name)}
                                disabled={roleLoading}
                                onChange={() => {
                                  if (handleCheck(ministry.name)) {
                                    // Get the role ID from the adminRole array
                                    const roleId = filteredAdmin.adminRole.find(
                                      (role: any) => role.role === ministry.name
                                    )._id;
                                    // If the admin has the role, remove it
                                    removeRoleHandler(filteredAdmin.id, roleId);
                                  } else {
                                    addRoleHandler({
                                      id: filteredAdmin.id,
                                      roleValue: ministry.name,
                                      ministryId: ministry._id,
                                    });
                                  }
                                }}
                              />
                            </td>
                          ))}
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan={5}
                      className="text-secondary text-center pt-2 text-sm"
                    >
                      No Admins Found!
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </section>
        </div>
      </div>
    </main>
  );
};
