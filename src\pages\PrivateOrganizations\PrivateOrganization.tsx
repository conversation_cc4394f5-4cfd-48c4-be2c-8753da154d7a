import { useEffect, useState } from "react";
import ActiveComponent from "../../components/stateAndBank/ActiveComponent";
import InactiveComponent from "../../components/stateAndBank/InactiveComponent";
import CreateModal from "../../components/stateAndBank/CreateModal";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import axios from "axios";

const PrivateOrganizations = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [allPrivateOrgs, setAllPrivateOrgs] = useState([]);
  const [selectedData, setSelectedData] = useState<any>({});
  const [selectedSubData, setSelectedSubdata] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [categoryId, setCategoryId] = useState("");
  const [subCategoryId, setSubCategoryId] = useState("");
  const [createMinistry, setCreateMinistry] = useState(false);

  const fetchCategories = async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "private organization"
      );
      setAllPrivateOrgs(data);
    } catch (error) {
      console.error("An error occurred: ", error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <>
      {selectedSubData.length > 0 ? (
        <ActiveComponent
          selectedData={selectedData}
          setSelectedData={setSelectedData}
          selectedSubData={selectedSubData}
          setSelectedSubdata={setSelectedSubdata}
          setShowCreateModal={setShowCreateModal}
          categoryId={categoryId}
          subCategoryId={subCategoryId}
          path={"privateOrganization"}
          setCreateMinistry={setCreateMinistry}
        />
      ) : (
        <InactiveComponent
          setSelectedData={setSelectedData}
          setSelectedSubdata={setSelectedSubdata}
          selectedData={selectedData}
          data={allPrivateOrgs}
          setCategoryId={setCategoryId}
          setSubCategoryId={setSubCategoryId}
          setShowCreateModal={setShowCreateModal}
          path={"privateOrganization"}
          setCreateMinistry={setCreateMinistry}
          createMinistry={createMinistry}
        />
      )}
      {showCreateModal && (
        <CreateModal
          setShowCreateModal={setShowCreateModal}
          selectedData={selectedData}
          categoryId={categoryId}
          subCategoryId={subCategoryId}
          path={"privateOrganization"}
          createMinistry={createMinistry}
          setCreateMinistry={setCreateMinistry}
        />
      )}
    </>
  );
};

export default PrivateOrganizations;
