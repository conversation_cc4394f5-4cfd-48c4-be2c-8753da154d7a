import { IoAdd, IoCamera, IoCheckmark } from "react-icons/io5";
import Modal, { ModalContent } from "../elements/Modal";
import { MdOutlineCancel } from "react-icons/md";
import { useState } from "react";
import axios from "axios";
import LoadingSpinner from "../elements/LoadingSpinner";
import toast from "react-hot-toast";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";

const CreateState = ({
  closeModal,
  path,
  createMinistry,
  data,
  fetchCategories,
}: any) => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [inputValue, setInputValue] = useState<{
    name: string;
    ministries: string;
    branches: string;
    selectState: string;
  }>({
    name: "",
    ministries: "",
    branches: "",
    selectState: "",
  });
  const [selectionArray, setSelectionArray] = useState<{ name: string }[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [fileInput, setFileInput] = useState<File | null>(null);
  const [file, setFile] = useState("");

  const AddToSelection = (item: string) => {
    if (item === "") return;
    setSelectionArray([...selectionArray, { name: item }]);
    if (path === "stateGovernment") {
      setInputValue({ ...inputValue, ministries: "" });
    } else {
      setInputValue({ ...inputValue, branches: "" });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFileInput(e.target.files[0]);
      setFile(URL.createObjectURL(e.target.files[0]));
    }
  };

  const GetImageURL = async () => {
    const formData = new FormData();
    if (fileInput) {
      formData.append("file", fileInput);
    } else {
      throw new Error("No file selected");
    }
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/upload/file`,
        formData
      );
      const url = res.data.url;
      return url;
    } catch (error) {
      console.error("Error uploading image:", error);
    }
  };

  const formValidation = () => {
    let hasErrors = false;
    if (!fileInput) {
      toast.error("Please upload an image");
      hasErrors = true;
    }
    if (inputValue.name === "") {
      toast.error("Please enter a name");
      hasErrors = true;
    }
    if (path === "stateGovernment" && selectionArray.length === 0) {
      toast.error("Please enter a ministry");
      hasErrors = true;
    }
    if (path === "mfbs" && selectionArray.length === 0) {
      toast.error("Please enter a branch");
      hasErrors = true;
    }
    return hasErrors;
  };

  const CreateHandler = () => {
    if (formValidation()) {
      toast.error("Please complete the form to add product");
      return;
    } else {
      GetImageURL().then(async (url) => {
        try {
          console.log("url", url);
          const payload = {
            category: inputValue.name,
            categoryIcon: url,
            categoryType: selectionArray,
          };

          setIsLoading(true);
          await axios.post(
            `${process.env.REACT_APP_API_URL}/onboardingCategory`,
            payload,
            {
              headers: {
                Authorization: token,
              },
            }
          );

          setShowSuccess(true);
          setTimeout(() => {
            setShowSuccess(false);
            setIsLoading(false);
            closeModal();
          }, 2000);
        } catch (error: any) {
          console.error("Error while creating:", error);
          toast.error(
            error.response?.data?.message || "Failed to create category"
          );
          setIsLoading(false);
        }
      });
    }
  };

  const CreateMinistry = async () => {
    if (inputValue.name === "" || inputValue.selectState === "") {
      toast.error("Please complete form");
      return;
    }
    setIsLoading(true);
    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${inputValue.selectState}/categoryType`,
        { name: inputValue.name },
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setShowSuccess(true);
      fetchCategories();
      setTimeout(() => {
        setShowSuccess(false);
        setIsLoading(false);
        closeModal();
      }, 2000);
    } catch (error: any) {
      console.error(error.message || "Something went wrong");
    }
  };

  return (
    <Modal
      open={true}
      onClose={closeModal}
      className="flex items-center justify-center"
    >
      <ModalContent className="mx-3 p-6 rounded-md shadow-lg bg-white w-[80%] md:w-[50%]">
        <div className="flex justify-end">
          <MdOutlineCancel
            className="text-2xl cursor-pointer"
            onClick={closeModal}
          />
        </div>

        {createMinistry ? (
          <section className="text-sm font-nunito px-5">
            <h2 className="text-lg">Create Ministry</h2>
            <div className="my-3">
              <label htmlFor="name">Ministry name</label>
              <input
                type="text"
                className="w-full border p-2 px-6 my-2 bg-gray-100"
                name="name"
                id="name"
                value={inputValue.name}
                onChange={(e) =>
                  setInputValue({ ...inputValue, name: e.target.value })
                }
                placeholder="Enter Ministry Name"
              />
            </div>
            <div className="mb-3">
              <label htmlFor="selectState">Select state</label>
              <select
                name="selectState"
                id="selectState"
                value={inputValue.selectState}
                onChange={(e) => {
                  setInputValue({ ...inputValue, selectState: e.target.value });
                }}
                className="w-full border p-2 px-6 my-2 bg-gray-100"
                required
              >
                <option value="">Select state</option>
                {data &&
                  data.map((item: any, index: number) => (
                    <option key={index} value={item._id}>
                      {item.category}
                    </option>
                  ))}
              </select>
            </div>
            <button
              type="button"
              className="bg-secondary text-white p-2 rounded-md w-2/3 mt-3 font-bold"
              onClick={CreateMinistry}
              disabled={isLoading}
            >
              {isLoading ? <LoadingSpinner /> : "Add Now"}
            </button>
          </section>
        ) : (
          <section className="text-sm font-nunito px-5">
            <h2>Create State</h2>
            <div className="my-10 w-fit">
              <figure className="w-20 h-20 flex items-center justify-center rounded-[50%] bg-gray-300 relative mb-4">
                {file ? (
                  <img
                    src={file}
                    className="rounded-[50%] h-full w-full object-cover"
                    alt=""
                  />
                ) : (
                  <>
                    <IoCamera className="w-6 h-6" />
                    <input
                      type="file"
                      className="absolute left-4 opacity-0 w-12 cursor-pointer"
                      onChange={handleFileChange}
                      accept="image/*"
                      name="logo"
                      id="logo"
                    />
                  </>
                )}
              </figure>
              <p className="text-sm text-secondary font-medium text-center">
                {path === "stateGovernment" ? "State" : "MFB"} logo
              </p>
            </div>
            <div className="relative">
              <label htmlFor="name" className="opacity-70">
                {path === "stateGovernment" ? "State" : "MFB"} Name
              </label>
              <input
                type="text"
                className="w-full border p-2 px-6 my-2 bg-gray-100"
                name="name"
                id="name"
                value={inputValue.name}
                onChange={(e) =>
                  setInputValue({ ...inputValue, name: e.target.value })
                }
                placeholder={`${
                  path === "stateGovernment" ? "State" : "MFB"
                } Name`}
              />
            </div>
            <div className="relative my-10">
              <label htmlFor="ministries" className="opacity-70">
                {path === "stateGovernment" ? "Ministries" : "Branches"}
              </label>
              <input
                type="text"
                className="w-full border p-2 px-6 my-2 bg-gray-100"
                name="ministries"
                id="ministries"
                value={
                  path === "stateGovernment"
                    ? inputValue.ministries
                    : inputValue.branches
                }
                onChange={(e) =>
                  path === "stateGovernment"
                    ? setInputValue({
                        ...inputValue,
                        ministries: e.target.value,
                      })
                    : setInputValue({
                        ...inputValue,
                        branches: e.target.value,
                      })
                }
                placeholder={
                  path === "stateGovernment"
                    ? "Min. of Agriculture, Min. of Health,"
                    : "Apapa, Ojo, Gbagada,"
                }
              />
              <button type="button" className="absolute top-9 -right-7">
                <IoAdd
                  className="text-secondary w-5 h-5 font-medium"
                  onClick={() =>
                    AddToSelection(
                      path === "stateGovernment"
                        ? inputValue.ministries
                        : inputValue.branches
                    )
                  }
                />
              </button>
              <p className="text-xs text-secondary">
                *Can add multiple Ministries
              </p>
              <p className={selectionArray.length > 0 ? "inline" : "hidden"}>
                {path === "stateGovernment"
                  ? "Ministries added"
                  : "Branches added"}
                :
              </p>
              <p>
                {selectionArray.map((item, index) => (
                  <span
                    key={index}
                    className="cursor-pointer"
                    onClick={() => {
                      const updatedArray = selectionArray.filter(
                        (_, i) => i !== index
                      );
                      setSelectionArray(updatedArray);
                    }}
                    title="Click to remove"
                  >
                    {item.name},{" "}
                  </span>
                ))}
              </p>
            </div>
            <button
              type="button"
              className="bg-secondary text-white p-2 rounded-md w-2/3 font-bold"
              onClick={CreateHandler}
              disabled={isLoading}
            >
              {isLoading ? <LoadingSpinner /> : "Add Now"}
            </button>
          </section>
        )}
        {showSuccess && (
          <Modal
            open={true}
            onClose={closeModal}
            className="flex items-center justify-center"
          >
            <ModalContent className="mx-3 p-6 rounded-md shadow-lg flex flex-col justify-center items-center bg-white w-[80%] md:w-[55%] lg:w-[50%] xl:w-[30%]">
              <IoCheckmark className="w-6 h-6 text-secondary" />
              {!createMinistry ? (
                <h1 className="font-semibold text-center pt-6 text-lg">
                  {path === "stateGovernment" ? "State" : "MFB"} has been added
                  successfully
                </h1>
              ) : (
                <h1 className="font-semibold text-center pt-6 text-lg">
                  Ministry has been added successfully
                </h1>
              )}
            </ModalContent>
          </Modal>
        )}
      </ModalContent>
    </Modal>
  );
};

export default CreateState;
