import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
// Assuming getSingleCustomer might be needed if detailed user info beyond name/email is required later,
// otherwise, it can be removed if userId object in the order already contains all needed info.
import {
  approveOrder,
  getOrders,
  getAllVendors,
  getSingleCustomer,
} from "../../redux/thunk";
import toast from "react-hot-toast";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import OrderDetails from "../../components/modals/OrderDetails"; // Re-uses the enhanced modal
import axios from "axios";
import { RootState } from "../../redux/store";
import Pagination from "../../components/Pagination";

const formatDate = (dateStr: string | undefined): string => {
  if (!dateStr || typeof dateStr !== "string") {
    return "Not specified";
  }

  try {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    const formattedDate = date.toLocaleDateString("en-US", options);

    return formattedDate;
  } catch (e) {
    console.error(`formatDate: Error formatting date "${dateStr}":`, e);
    return dateStr;
  }
};

export const ChurchOrders = () => {
  const [churchOrdersList, setChurchOrdersList] = useState<any[]>([]);
  const [viewDetails, setViewDetails] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [error, setError] = useState<any>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const { token } = useAppSelector((store: RootState) => store.auth);

  const dispatch = useAppDispatch();
  const { orders } = useAppSelector((store) => store.orders);
  const ordersLoading = false;

  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;

  useEffect(() => {
    dispatch(getOrders());
    dispatch(getAllVendors());
  }, [dispatch]);

  // Filter for Church Orders
  useEffect(() => {
    if (orders.length > 0) {
      const churchOrders = orders.filter(
        (order: any) => order.userId && order.userId.categoryType === "church"
      );
      setChurchOrdersList(churchOrders);
    } else {
      setChurchOrdersList([]);
    }
  }, [orders]);

  const currentOrders = churchOrdersList.slice(
    indexOfFirstPost,
    indexOfLastPost
  );
  const totalPages = Math.ceil(churchOrdersList.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleViewDetails = (order: any) => {
    setSelectedOrder(order);
    setViewDetails(true);
  };

  const handleCloseDetails = () => {
    setViewDetails(false);
    setSelectedOrder(null);
    setError({});
  };

  const handleApprove = async (orderId: string) => {
    setIsActionLoading(true);
    try {
      const resultAction = await dispatch(approveOrder(orderId));
      if (approveOrder.fulfilled.match(resultAction)) {
        toast.success("Order approved successfully");
        dispatch(getOrders());
        setViewDetails(false);
      } else if (approveOrder.rejected.match(resultAction)) {
        toast.error(
          (resultAction.payload as string) || "Failed to approve order"
        );
      }
    } catch (error) {
      toast.error("An error occurred while approving the order");
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleInitialApprove = async (
    id: string,
    pickUpDetailsInput: string
  ) => {
    setError({});
    if (pickUpDetailsInput.trim().length === 0) {
      setError({ initialPickup: "Enter pickup location" });
      return;
    }

    const payload = {
      orderId: id,
      pickUpDetails: pickUpDetailsInput,
    };

    setIsActionLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/approveOrderInitial`,
        payload,
        { headers: { Authorization: token } }
      );
      toast.success(res.data.message || "Order approved successfully");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Initial Approve Error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to approve order!"
      );
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleFinalApprove = async (
    id: string,
    deliveryDetailsInput: string
  ) => {
    setError({});
    if (deliveryDetailsInput.trim().length === 0) {
      setError({ finalDelivery: "Enter delivery details" });
      return;
    }

    const payload = {
      orderId: id,
      deliveryDetails: deliveryDetailsInput,
    };

    setIsActionLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/approveOrderFinal`,
        payload,
        { headers: { Authorization: token } }
      );
      toast.success(res.data.message || "Order finalized successfully");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Final Approve Error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to finalize order!"
      );
    } finally {
      setIsActionLoading(false);
    }
  };

  if (ordersLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <main className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Church Orders</h1>
        <div className="text-sm text-gray-600">
          Total Orders: {churchOrdersList.length}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-left border-collapse">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 font-semibold text-gray-700">S/N</th>
                <th className="p-4 font-semibold text-gray-700">Order ID</th>
                <th className="p-4 font-semibold text-gray-700">Customer</th>
                <th className="p-4 font-semibold text-gray-700">Email</th>
                <th className="p-4 font-semibold text-gray-700">
                  Total Amount
                </th>
                <th className="p-4 font-semibold text-gray-700">
                  Date Created
                </th>
                <th className="p-4 font-semibold text-gray-700">Status</th>
                <th className="p-4 font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentOrders.length > 0 ? (
                currentOrders.map((order: any, index: number) => (
                  <tr key={order._id} className="border-b hover:bg-gray-50">
                    <td className="p-4">{indexOfFirstPost + index + 1}</td>
                    <td className="p-4 font-mono text-sm">{order._id}</td>
                    <td className="p-4">
                      {order.userId
                        ? `${order.userId.firstName} ${order.userId.lastName}`
                        : "N/A"}
                    </td>
                    <td className="p-4">{order.userId?.email || "N/A"}</td>
                    <td className="p-4 font-semibold">
                      ₦{order.totalAmount?.toLocaleString() || "0"}
                    </td>
                    <td className="p-4">{formatDate(order.createdAt)}</td>
                    <td className="p-4">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          order.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : order.status === "approved"
                            ? "bg-green-100 text-green-800"
                            : order.status === "delivered"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {order.status || "pending"}
                      </span>
                    </td>
                    <td className="p-4">
                      <button
                        onClick={() => handleViewDetails(order)}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="p-8 text-center text-gray-500">
                    No church orders found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {churchOrdersList.length > itemsPerPage && (
          <div className="p-4 border-t">
            <Pagination
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
              length={churchOrdersList.length}
              handlePagination={handlePageChange}
              prevPage={handlePrevPage}
              nextPage={handleNextPage}
            />
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {viewDetails && selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          onClose={handleCloseDetails}
          onApprove={handleApprove}
          onInitialApprove={handleInitialApprove}
          onFinalApprove={handleFinalApprove}
          isLoading={isActionLoading}
          error={error}
        />
      )}
    </main>
  );
};
