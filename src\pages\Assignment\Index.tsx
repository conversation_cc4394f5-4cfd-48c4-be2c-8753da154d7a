import { FaChessKing, FaChessQueen } from "react-icons/fa6";
import { Link } from "react-router-dom";

const Index = () => {
  return (
    <main>
      <h1 className="font-bold text-xl">Assignment of Duties</h1>

      <section className="flex mt-5 justify-evenly flex-wrap gap-3">
        <Link to="admin-assignment">
          <button
            className={`bg-white p-5 rounded-md border cursor-pointer border-green-300 shadow-md w-60 flex items-center justify-center flex-col gap-3`}
          >
            <FaChessKing className="w-7 h-7 text-secondary" />
            <h2 className="text-sm font-semibold pb-4 text-gray-700">Admins</h2>
          </button>
        </Link>
        <Link to="sales-admin-assignment">
          <button
            className={`bg-white p-5 rounded-md border cursor-pointer border-green-300 shadow-md w-60 flex items-center justify-center flex-col gap-3`}
          >
            <FaChessQueen className="w-7 h-7 text-secondary" />
            <h2 className="text-sm font-semibold pb-4 text-gray-700">
              Sales Admins
            </h2>
          </button>
        </Link>
      </section>
    </main>
  );
};

export default Index;
