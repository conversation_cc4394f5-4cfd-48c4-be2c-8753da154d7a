import { ChangeEvent, useEffect, useState } from "react";
import LoadingSpinner from "../elements/LoadingSpinner";
import axios from "axios";
import { FaPhone, FaRegCopy, FaWhatsapp } from "react-icons/fa6";
import toast from "react-hot-toast";
import { Preloader } from "../elements/Preloader";

const formatDeliveryDate = (dateStr: string | undefined): string => {
  if (!dateStr || typeof dateStr !== "string") {
    return "Not specified";
  }

  try {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    const formattedDate = date.toLocaleDateString("en-US", options);

    return formattedDate;
  } catch (e) {
    console.error(`formatDeliveryDate: Error formatting date "${dateStr}":`, e);
    return dateStr;
  }
};

const OrderDetails = ({
  accountType,
  orderDetails,
  setViewDetails,
  pickupDetails,
  setPickupDetails,
  handleApproveOrder,
  handleInitialApprove,
  error,
  setError,
  isLoading,
}: any) => {
  const [vendorsWithProduct, setVendorsWithProduct] = useState<
    Record<string, any[] | string>
  >({});
  const [vendorsLoading, setVendorsLoading] = useState<Record<string, boolean>>(
    {}
  );
  const [initialPickupLocation, setInitialPickupLocation] = useState(
    orderDetails?.pickUpDetails || ""
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const initialDetails = orderDetails.orderItems.map((item: any) => {
      getVendorsWithProduct(item.productId); // Fetch vendors for each product
      // Pre-fill vendor if already assigned (for approved/delivered orders)
      const assignedVendorId =
        item.pickUpDetails?._id || item.pickUpDetails?.vendorId || "";
      return { productId: item.productId, vendorId: assignedVendorId };
    });
    setPickupDetails(initialDetails);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderDetails.orderItems]);

  const handleCopyClick = async (text: any) => {
    if (!text) return;
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy: ", err);
      toast.error("Failed to copy.");
    }
  };

  const getVendorsWithProduct = async (productId: string) => {
    if (!productId || vendorsWithProduct[productId]) return;

    setVendorsLoading((prev) => ({ ...prev, [productId]: true }));
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/vendors-product/${productId}`
      );
      setVendorsWithProduct((prev) => ({
        ...prev,
        [productId]: res.data.length > 0 ? res.data : "not found",
      }));
    } catch (error: any) {
      console.error(`Error fetching vendors for ${productId}:`, error);
      setVendorsWithProduct((prev) => ({
        ...prev,
        [productId]: "error",
      }));
    } finally {
      setVendorsLoading((prev) => ({ ...prev, [productId]: false }));
    }
  };

  const handleClose = () => {
    setError({});
    setViewDetails(false);
  };

  const handleSelectVendor = (
    e: ChangeEvent<HTMLSelectElement>,
    productId: string
  ) => {
    setError({});
    const selectedVendorId = e.target.value;
    setPickupDetails((prev: any[]) => {
      return prev.map((item: any) => {
        if (item.productId === productId) {
          return { ...item, vendorId: selectedVendorId };
        }
        return item;
      });
    });
  };

  const triggerApproveOrder = async () => {
    setIsSubmitting(true);
    await handleApproveOrder(orderDetails._id);
    setIsSubmitting(false);
  };

  const triggerInitialApprove = async () => {
    setIsSubmitting(true);
    await handleInitialApprove(orderDetails._id, initialPickupLocation);
    setIsSubmitting(false);
  };

  return (
    <div className="fixed z-50 inset-0 bg-gray-900 bg-opacity-60 overflow-y-auto flex justify-center items-start pt-10 px-4">
      <div className="bg-white p-5 rounded-lg shadow-xl relative mx-auto my-5 w-full max-w-2xl">
        {/* Header */}
        <div className="flex justify-between items-center pb-3 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Order Details</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-800"
          >
            {/* SVG Close Icon */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Body */}
        <div className="py-4">
          {/* Order Items Table */}
          <h3 className="text-md font-semibold mb-2 text-gray-700">
            Items Ordered
          </h3>
          <div className="overflow-x-auto mb-4">
            <table className="w-full mb-4 min-w-[500px]">
              <thead className="text-left bg-gray-50">
                <tr className="leading-10">
                  <th className="p-2 text-sm font-semibold text-gray-600"></th>
                  <th className="p-2 text-sm font-semibold text-gray-600">
                    Product
                  </th>
                  <th className="p-2 text-sm font-semibold text-gray-600">
                    Qty
                  </th>
                  <th className="p-2 text-sm font-semibold text-gray-600">
                    Price
                  </th>
                  <th className="p-2 text-sm font-semibold text-gray-600">
                    {orderDetails.status !== "pending"
                      ? "Assigned Vendor"
                      : "Assign Vendor"}
                  </th>
                </tr>
              </thead>
              <tbody className="text-sm text-gray-700">
                {orderDetails.orderItems.map((item: any, index: number) => (
                  <tr
                    key={item._id || index}
                    className="border-b border-gray-200"
                  >
                    <td className="py-2 px-1 w-14 align-top">
                      <img
                        className="w-full rounded"
                        src={item.image}
                        alt={item.name}
                      />
                    </td>
                    <td className="py-2 px-2 align-top">
                      <span className="font-medium">{item.name}</span>
                      <br />
                      <span className="text-xs text-gray-500">
                        {item.measurement}
                      </span>
                    </td>
                    <td className="py-2 px-2 align-top">{item.quantity}</td>
                    <td className="py-2 px-2 align-top">
                      ₦{item.totalPrice?.toLocaleString()}
                    </td>
                    <td className="py-2 px-2 align-top">
                      {/* Vendor Selection/Display */}
                      {orderDetails.status === "pending" ? (
                        // --- Vendor Selection Dropdown ---
                        vendorsLoading[item.productId] ? (
                          <span className="text-xs text-gray-500">
                            Loading...
                          </span>
                        ) : vendorsWithProduct[item.productId] &&
                          Array.isArray(vendorsWithProduct[item.productId]) ? (
                          <select
                            name={`vendor-${item.productId}`}
                            id={`vendor-${item.productId}`}
                            className="w-full p-2 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-1 focus:ring-secondary"
                            disabled={orderDetails.status !== "pending"}
                            value={
                              pickupDetails?.find(
                                (pd: any) => pd.productId === item.productId
                              )?.vendorId || ""
                            }
                            onChange={(e) =>
                              handleSelectVendor(e, item.productId)
                            }
                          >
                            <option value="">Select Vendor</option>
                            {(
                              vendorsWithProduct[item.productId] as Array<any>
                            )?.map((vendor: any) => (
                              <option
                                key={vendor._id}
                                value={vendor._id}
                                title={vendor.address}
                              >
                                {vendor.fullName}
                              </option>
                            ))}
                          </select>
                        ) : vendorsWithProduct[item.productId] ===
                          "not found" ? (
                          <span className="text-xs text-red-500">
                            No vendors
                          </span>
                        ) : (
                          // ) : vendorsWithProduct[item.productId] === "error" ? (
                          //   <span className="text-xs text-red-500">Error</span>
                          <span className="text-xs text-gray-500">
                            Loading...
                          </span>
                        )
                      ) : (
                        // --- End Vendor Selection ---
                        // --- Display Assigned Vendor ---
                        <span className="text-xs">
                          {item.pickUpDetails?.fullName || (
                            <span className="text-gray-400">N/A</span>
                          )}
                          {item.pickUpDetails?.address && (
                            <p className="text-gray-500 text-[10px]">
                              {item.pickUpDetails.address}
                            </p>
                          )}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Display vendor selection error */}
          {error.pickupDetails && (
            <p className="text-xs text-red-500 text-right font-semibold mb-3 -mt-2">
              {error.pickupDetails}
            </p>
          )}

          {/* Order Summary & Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            {/* Left Side: Totals */}
            <div className="text-sm space-y-1">
              <div className="flex items-center justify-between text-gray-600">
                <p>Subtotal:</p>
                <p>₦{orderDetails?.subtotal?.toLocaleString() || 0}</p>
              </div>
              <div className="flex items-center justify-between text-gray-600">
                <p>Service Fee:</p>
                <p>₦{orderDetails?.serviceFee?.toLocaleString() || 0}</p>
              </div>
              {accountType === "flexible" && (
                <div className="flex items-center justify-between text-gray-600">
                  <p>Interest:</p>
                  <p>₦{orderDetails?.interest?.toLocaleString() || 0}</p>
                </div>
              )}
              <div className="flex items-center justify-between text-gray-600">
                <p>Delivery Fee:</p>
                <p>₦{orderDetails?.deliveryFee?.toLocaleString() || 0}</p>
              </div>
              <hr className="my-1" />
              <div className="flex items-center justify-between text-base text-gray-800 font-semibold">
                <p>Total:</p>
                <p>
                  ₦{orderDetails?.allItemsTotalPrice?.toLocaleString() || 0}
                </p>
              </div>
              <p className="text-right text-xs text-gray-500 pt-1">
                Ordered:{" "}
                {orderDetails.orderDate
                  ? new Date(orderDetails.orderDate).toLocaleString("en-US", {
                      dateStyle: "medium",
                      timeStyle: "short",
                    })
                  : new Date(orderDetails.createdAt).toLocaleString("en-US", {
                      dateStyle: "medium",
                      timeStyle: "short",
                    })}
              </p>
              {/* --- Delivery Option Display --- */}
              <div className="flex items-center justify-between text-sm text-gray-600 pt-2">
                <p className="font-medium text-gray-700">Requested Delivery:</p>
                <p className="font-semibold">
                  {formatDeliveryDate(
                    orderDetails?.userId?.deliveryDateOption ||
                      orderDetails?.deliveryDateOption
                  )}
                </p>
              </div>
            </div>

            {/* Right Side: Customer & Delivery Info */}
            <div className="text-sm space-y-3">
              <div>
                <p className="font-medium text-gray-700 mb-1">
                  Delivery Address:
                </p>
                <p className="text-gray-600">
                  {orderDetails?.deliveryDetails || "Not Provided"}
                </p>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">
                  Primary Contact:
                </p>
                <div className="flex items-center gap-4 bg-gray-50 p-2 rounded">
                  <span className="text-gray-800">
                    {orderDetails?.userId?.phoneNumber || "N/A"}
                  </span>
                  {orderDetails?.userId?.phoneNumber && (
                    <div className="flex gap-3 text-secondary">
                      <a
                        href={`tel:${orderDetails.userId.phoneNumber}`}
                        title="Call"
                        className="hover:text-secondary-dark"
                      >
                        <FaPhone className="w-4 h-4" />
                      </a>
                      <a
                        href={`https://wa.me/${orderDetails.userId.phoneNumber.replace(
                          /^0/,
                          "234"
                        )}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        title="WhatsApp"
                        className="hover:text-secondary-dark"
                      >
                        <FaWhatsapp className="w-4 h-4" />
                      </a>
                      <button
                        type="button"
                        title="Copy Phone Number"
                        className="hover:text-secondary-dark"
                        onClick={() =>
                          handleCopyClick(orderDetails.userId.phoneNumber)
                        }
                      >
                        <FaRegCopy className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* --- Display Additional Contacts (deliveryContact2) --- */}
              {orderDetails?.deliveryContact2 &&
                Array.isArray(orderDetails.deliveryContact2) &&
                orderDetails.deliveryContact2.length > 0 && (
                  <div>
                    <p className="font-medium text-gray-700 mb-1">
                      Additional Contacts:
                    </p>
                    <ul className="space-y-1 list-disc list-inside pl-1">
                      {orderDetails.deliveryContact2.map(
                        (phone: string, idx: number) => (
                          <li
                            key={idx}
                            className="text-gray-600 flex items-center gap-3 text-xs"
                          >
                            <span>{phone}</span>
                            <a
                              href={`tel:${phone}`}
                              title="Call"
                              className="text-secondary hover:text-secondary-dark"
                            >
                              <FaPhone className="w-3 h-3" />
                            </a>
                            <button
                              type="button"
                              title="Copy Phone Number"
                              className="text-secondary hover:text-secondary-dark"
                              onClick={() => handleCopyClick(phone)}
                            >
                              <FaRegCopy className="w-3 h-3" />
                            </button>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </div>

          {/* Pickup Location & Initial Approve (only for pending orders) */}
          {orderDetails.status === "pending" && !orderDetails.pickUpDetails && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <label
                htmlFor="pickupLocation"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Set Initial Pickup Location:{" "}
                <span className="text-gray-500 text-xs">
                  (Required before assigning vendors)
                </span>
              </label>
              <textarea
                name="pickupLocation"
                id="pickupLocation"
                placeholder="Enter initial pickup location details (e.g., Warehouse A, Section 3)"
                className="border border-gray-300 w-full p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary"
                rows={2}
                value={initialPickupLocation}
                onChange={(e) => {
                  setInitialPickupLocation(e.target.value);
                  setError((prev: any) => ({
                    ...prev,
                    initialPickup: undefined,
                  }));
                }}
              ></textarea>
              {error.initialPickup && (
                <p className="text-xs text-red-500 mt-1">
                  {error.initialPickup}
                </p>
              )}
              <div className="text-right mt-2">
                <button
                  type="button"
                  className="bg-blue-600 hover:bg-blue-700 text-sm rounded-md py-2 px-4 text-white inline-flex items-center justify-center w-32 transition-colors disabled:bg-blue-300"
                  onClick={triggerInitialApprove}
                  disabled={
                    !initialPickupLocation.trim() || isSubmitting || isLoading
                  }
                >
                  {isSubmitting || isLoading ? (
                    <Preloader />
                  ) : (
                    "Set & Approve Initial"
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {orderDetails.status === "pending" && orderDetails.pickUpDetails && (
          <div className="pt-4 border-t border-gray-200 flex justify-end gap-3">
            {/* Decline button might not be needed if Close button exists */}
            {/* <button
              className="p-2 px-4 rounded-md text-sm text-gray-700 bg-gray-200 hover:bg-gray-300 transition-colors"
              onClick={handleClose}
            >
              Decline/Close
            </button> */}
            <button
              className={`py-2 px-4 rounded-md text-sm text-white w-28 flex justify-center items-center transition-colors ${
                pickupDetails.some((item: any) => !item.vendorId) ||
                isSubmitting ||
                isLoading
                  ? "bg-green-300 cursor-not-allowed" // Disabled style
                  : "bg-secondary hover:bg-secondary-dark" // Enabled style
              }`}
              onClick={triggerApproveOrder} // Use the wrapper
              disabled={
                pickupDetails.some((item: any) => !item.vendorId) ||
                isSubmitting ||
                isLoading
              } // Disable if any vendor not selected or submitting
            >
              {isSubmitting || isLoading ? <LoadingSpinner /> : "Approve Order"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderDetails;
