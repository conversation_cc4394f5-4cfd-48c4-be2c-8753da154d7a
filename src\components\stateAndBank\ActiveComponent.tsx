import { useEffect, useState } from "react";
import {
  IoAdd,
  IoArrowForward,
  IoMenu,
  IoSearchOutline,
} from "react-icons/io5";
import LoadingSpinner from "../elements/LoadingSpinner";
import { FaRegCopy } from "react-icons/fa6";
import toast from "react-hot-toast";
import { AddBankModal } from "../modals/AddBankModal";
import { AllBanksModal } from "../modals/AllBanksModal";
import UpdateCreditLimit from "../modals/UpdateCreditLimit";
import axios from "axios";
import { formatNumber } from "../../redux/thunk";
import { useAppSelector } from "../../redux/hooks";

const ActiveComponent = ({
  selectedData,
  setSelectedData,
  selectedSubData,
  setSelectedSubdata,
  setShowCreateModal,
  setCreateMinistry,
  subCategoryId,
  categoryId,
  path,
}: any) => {
  const [isLoading, setIsLoading] = useState(false);
  const [searchedUser, setSearchedUser] = useState<any>([]);
  const { token } = useAppSelector((store) => store.auth);
  const [searchResults, setSearchResults] = useState<any>([]);
  const [filter, setFilter] = useState("all");
  const [userToAddBank, setUserToAddBank] = useState<any>({});
  const [addBankModal, setAddBankModal] = useState(false);
  const [allBanksDetails, setAllBanksDetails] = useState<any>([]);
  const [userId, setUserId] = useState("");
  const [allBanksModal, setAllBanksModal] = useState(false);
  const adminLevel = sessionStorage.getItem("adminLevel");
  const [userData, setUserData] = useState<any>({});
  const [viewUpdateCreditLimit, setViewUpdateCreditLimit] = useState(false);
  const [data, setData] = useState([]);

  const searchForUsersWithEmail = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const searchResult =
        data?.filter((user: any) =>
          user.email?.toLowerCase().includes(value.toLowerCase())
        ) || null;
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  const handleCopyClick = async (text: any) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  const handleAddBank = (firstName: any, lastName: any, id: string) => {
    setUserToAddBank({ name: lastName + " " + firstName, id });
    setAddBankModal((prev) => !prev);
  };

  const handleViewAllBank = (id: string, linkedBanks: any) => {
    setUserId(id);
    setAllBanksDetails(linkedBanks);
    setAllBanksModal((prev) => !prev);
  };

  const handleUpdateCreditLimit = (data: any) => {
    setUserData(data);
    setViewUpdateCreditLimit((prev) => !prev);
  };

  const GetAllUsers = async () => {
    let endPointUrl = "";

    if (path === "stateGovernment") {
      endPointUrl = "getCustomersByStateGovAndMinistryId";
    } else if (path === "church") {
      endPointUrl = "getCustomersByChurchAndMinistryId";
    } else if (path === "privateOrganization") {
      endPointUrl = "getCustomersByPrivateOrgAndMinistryId";
    }

    if (!endPointUrl) {
      console.error("Invalid path provided:", path);
      return;
    }

    setIsLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/${endPointUrl}/${categoryId}/${subCategoryId}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setData(res.data);
    } catch (error: any) {
      console.error(error.message || `Failed to fetch data: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    GetAllUsers();
  }, []);

  return (
    <main className="w-full">
      <div className="bg-white rounded-md shadow-md pb-6">
        {adminLevel === "superadmin" && (
          <section className="flex justify-end p-2 gap-2">
            <button
              type="button"
              className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
              onClick={() => {
                setShowCreateModal(true);
                setCreateMinistry(true);
              }}
            >
              Create{" "}
              {path === "stateGovernment"
                ? "Ministry"
                : path === "church"
                ? "Department"
                : path === "privateOrganization"
                ? "Department"
                : "Ministry"}
            </button>
            <button
              type="button"
              className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
              onClick={() => setShowCreateModal(true)}
            >
              Create{" "}
              {path === "stateGovernment"
                ? "State"
                : path === "church"
                ? "Church"
                : path === "privateOrganization"
                ? "Organization"
                : "Bank"}
            </button>
          </section>
        )}

        <div
          className={`w-full overflow-x-auto ${
            isLoading && "animate-pulse h-[50vh]"
          }`}
        >
          <section className="flex gap-3">
            <section className="px-2 py-5 shadow-md flex flex-col items-center">
              <button
                type="button"
                onClick={() => {
                  setSelectedSubdata("");
                  setSelectedData({
                    name: "",
                    logo: "",
                    ministries: [],
                  });
                }}
              >
                <IoMenu className="w-6 h-6" />
              </button>
              <figure className="w-10 h-10 my-3" title={selectedData.category}>
                <img
                  src={selectedData.categoryIcon}
                  alt={selectedData.category}
                />
              </figure>
            </section>
            <section>
              <div
                className="flex items-center justify-between px-6 py-3 font-sans"
                style={{ minWidth: "700px" }}
              >
                <div className="flex justify-between items-center w-full">
                  <h1 className="text-sm font-medium flex items-center gap-2">
                    Dashboard
                    <span className="opacity-60 flex items-center gap-2">
                      <IoArrowForward /> {selectedData.category}
                      <IoArrowForward /> {selectedSubData}
                    </span>
                  </h1>
                  <div className="relative md:w-[30rem] w-fit">
                    <IoSearchOutline className="w-5 h-5 absolute top-[0.6rem] left-2 text-gray-300" />
                    <input
                      type="search"
                      name="searchedUser"
                      id="searchedUser"
                      value={searchedUser}
                      onChange={(e) => searchForUsersWithEmail(e.target.value)}
                      placeholder="Search user using email"
                      className="border p-2 text-sm rounded-md indent-7 w-full"
                      disabled={data.length === 0}
                    />
                  </div>

                  <div>
                    <label htmlFor="filterOccupation">
                      <select
                        name="filterOccupation"
                        id="filterOccupation"
                        className="border p-2 ml-4 text-xs relative"
                        onChange={(e) => setFilter(e.target.value)}
                      >
                        <option value="all">All</option>
                        <option value="employed">Employed</option>
                        <option value="self-employed">Self-employed</option>
                        <option value="student">Students</option>
                      </select>
                    </label>
                  </div>
                </div>
              </div>
              <section className="overflow-x-auto font-mont">
                <table className="md:w-[1350px] w-[700px] my-3 text-nowrap text-xs">
                  <thead className="bg-gray-50 font-bold text-left p-4">
                    <tr>
                      <th className="p-2 ">S/N</th>
                      <th className="p-2 ">Name</th>
                      <th className="p-2">Email</th>
                      <th className="p-2">Phone number</th>
                      <th className="p-2">BVN</th>
                      <th className="p-2">All Banks</th>
                      <th className="p-2">Credit Score Bal.</th>
                      <th className="p-2">Credit Score</th>
                      <th className="p-2">Open Password</th>
                      <th className="p-2 text-center">Action</th>
                      <th className="p-2">Force Delete User</th>
                      <th className="p-2 text-secondary flex items-center gap-2">
                        <IoAdd /> Add Bank
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={12} className="text-center p-3">
                          <LoadingSpinner />
                        </td>
                      </tr>
                    ) : data && data.length > 0 ? (
                      data
                        .slice()
                        .reverse()
                        .map((data: any, index: any) => (
                          <tr className="border-b border-gray-300" key={index}>
                            <td className="text-secondary py-4 px-2">
                              {index + 1}
                            </td>
                            <td className="py-4 px-2">
                              {`${data.firstName} ${data.lastName}` ||
                                "--- ---"}
                            </td>
                            <td className="py-4 px-2">
                              <div className="flex justify-between gap-2 items-center">
                                <p>{data.email || "---"}</p>
                                <button
                                  type="button"
                                  className="mr-3"
                                  onClick={() => handleCopyClick(data.email)}
                                >
                                  <FaRegCopy className="w-5 h-5 text-gray-500" />
                                </button>
                              </div>
                            </td>
                            <td className="py-4 px-2">
                              {data.phoneNumber || "---"}
                            </td>
                            <td className="py-4 px-2">
                              <div className="flex justify-between gap-2 items-center">
                                <p>{data.bvn || "---"}</p>
                                <button
                                  type="button"
                                  className="mr-3"
                                  onClick={() => handleCopyClick(data.bvn)}
                                >
                                  <FaRegCopy className="w-5 h-5 text-gray-500" />
                                </button>
                              </div>
                            </td>
                            <td className="py-4 px-2">
                              <button
                                type="button"
                                className="text-secondary p-2 w-full flex justify-center"
                                onClick={() =>
                                  handleViewAllBank(data._id, data.linkedBanks)
                                }
                              >
                                View
                              </button>
                            </td>
                            <td className="py-4 px-2">
                              {data.creditScore
                                ? "₦" + formatNumber(data.creditScore)
                                : "---"}
                            </td>
                            <td className="py-4 px-2">
                              {data.creditLimit
                                ? "₦" + formatNumber(data.creditLimit)
                                : "---"}
                            </td>
                            <td className="py-4 px-2">{data.userPassword}</td>
                            <td className="py-4 px-2">
                              <button
                                type="button"
                                className="text-secondary p-2 w-full flex justify-center"
                                onClick={() => handleUpdateCreditLimit(data)}
                              >
                                Update Credit Limit
                              </button>
                            </td>
                            <td className="py-4 px-2">
                              <button
                                type="button"
                                className="w-full flex justify-center text-red-500"
                              >
                                Delete
                              </button>
                            </td>
                            <td className="py-4 px-2">
                              <button
                                type="button"
                                className="w-full flex justify-center text-secondary text-sm"
                                onClick={() =>
                                  handleAddBank(
                                    data.firstName,
                                    data.lastName,
                                    data._id
                                  )
                                }
                              >
                                <IoAdd />
                              </button>
                            </td>
                          </tr>
                        ))
                    ) : (
                      <tr>
                        <td colSpan={12} className="text-center p-3">
                          No data found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </section>
            </section>
          </section>
        </div>
      </div>
      {addBankModal && (
        <AddBankModal
          setAddBankModal={setAddBankModal}
          userToAddBank={userToAddBank}
        />
      )}

      {allBanksModal && (
        <AllBanksModal
          setAllBanksModal={setAllBanksModal}
          setAllBanksDetails={setAllBanksDetails}
          allBanksDetails={allBanksDetails}
          userId={userId}
        />
      )}

      {viewUpdateCreditLimit && (
        <UpdateCreditLimit
          userData={userData}
          closeModal={() => setViewUpdateCreditLimit(false)}
        />
      )}
    </main>
  );
};

export default ActiveComponent;
